using System;
using System.Collections;
using System.Collections.Generic;
using EOP.Utilites;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;
using EOP.combat;
using Sirenix.Utilities;
using Unity.VisualScripting;

namespace EOP.Tetris
{
    public struct DexterityEvent
    {
    }

    public class TetrominoEntity : MonoBehaviour
    {
        public float fallTime;
        public TetrominoSO tetrominoSO;
        public GameObject tetrominoShadowPrefab;

        [Header("Stats"), Space(10)] [ReadOnly]
        public Mino[] _minos;

        [ReadOnly] public int rotateNum;
        [ReadOnly, ShowInInspector] private float startTime;
        [ReadOnly] public float lastFallTime;
        [ReadOnly] public float timeUsed;
        [ReadOnly] public string colorName;
        [ReadOnly] public string shapeName;
        [ReadOnly] public string tetrominoName;

        private Vector3 _rotatePoint;
        private float _lastFallTime;
        private TetrisGrid _grid;
        private TetrisSelector _selector;
        private TetrominoShadow _tetrominoShadow;
        private float _lifeTime;

        public string skillName;

        public void Initialize(TetrisGrid grid, TetrisSelector selector)
        {
            tetrominoName = colorName + shapeName;
            _grid = grid;
            _selector = selector;
            _minos = GetComponentsInChildren<Mino>();

            // Settings using TetrominoSO
            for (int i = 0; i < _minos.Length; i++)
            {
                _minos[i].transform.localPosition = (Vector2)tetrominoSO.minosLocalPos[i];
            }

            _rotatePoint = tetrominoSO.rotatePoint;

            // Cast Shadow
            _tetrominoShadow = Instantiate(tetrominoShadowPrefab, transform).GetComponent<TetrominoShadow>();
            _tetrominoShadow.CastShadow(this, _minos);

            SetMinosColor(colorName);
            fallTime = CombatManager.fallTime;
        }

        public void Initialize2(TetrisGrid grid)
        {
            tetrominoName = colorName + shapeName;
            _grid = grid;

            _minos = GetComponentsInChildren<Mino>();
            _tetrominoShadow = Instantiate(tetrominoShadowPrefab, transform).GetComponent<TetrominoShadow>();
            _tetrominoShadow.CastShadow(this, _minos);
            SetMinosColor(colorName);
            fallTime = CombatManager.fallTime;

            // Scale all minos to match the grid
            ScaleMinosToGrid();
        }

        private void OnEnable()
        {
            _minos.ForEach(m => m.enabled = true);
        }

        private void OnDisable()
        {
            if (_lifeTime <= 1f)
            {
                EventBetter.Raise(new DexterityEvent());
            }

            startTime = Time.time;

            //_minos.ForEach(m => m.enabled = false);
        }

        public void SetMinosColor(string color)
        {
            foreach (var mino in _minos)
            {
                mino.SetMinosColor(color);
            }
        }


        public void Trigger(List<Mino> minos)
        {
            foreach (var mino in minos)
            {
                mino.Trigger();
            }

            _grid.LineClearedAndAction(skillName, minos.Count);
            //Debug.Log($"Triggered {minos.Count} minos");
        }

        private void Update()
        {
            _lifeTime += Time.deltaTime;

            if (UnityEngine.Input.GetKeyDown(KeyCode.A))
            {
                transform.Translate(Vector3.left * _grid.gridSize, Space.World);
                if (!IsValidMove())
                {
                    transform.Translate(Vector3.right * _grid.gridSize, Space.World);
                }
                else
                {
                    _tetrominoShadow.CastShadow(this, _minos);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.D))
            {
                transform.Translate(Vector3.right * _grid.gridSize, Space.World);
                if (!IsValidMove())
                {
                    transform.Translate(Vector3.left * _grid.gridSize, Space.World);
                }
                else
                {
                    _tetrominoShadow.CastShadow(this, _minos);
                }
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.Q))
            {
                transform.RotateAround(transform.TransformPoint(_rotatePoint), Vector3.forward, 90f);
                rotateNum++;
                if (!IsValidMove())
                {
                    transform.RotateAround(transform.TransformPoint(_rotatePoint), Vector3.forward, -90f);
                    rotateNum--;
                }
                else
                {
                    _tetrominoShadow.CastShadow(this, _minos);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.E) || UnityEngine.Input.GetKeyDown(KeyCode.W))
            {
                transform.RotateAround(transform.TransformPoint(_rotatePoint), Vector3.forward, -90f);
                rotateNum++;
                if (!IsValidMove())
                {
                    transform.RotateAround(transform.TransformPoint(_rotatePoint), Vector3.forward, 90f);
                    rotateNum--;
                }
                else
                {
                    _tetrominoShadow.CastShadow(this, _minos);
                }
            }

            if (Time.time - _lastFallTime > (UnityEngine.Input.GetKey(KeyCode.S) ? fallTime / 10 : fallTime))
            {
                _lastFallTime = Time.time;
                transform.Translate(Vector3.down * _grid.gridSize, Space.World);
                if (!IsValidMove())
                {
                    transform.Translate(Vector3.up * _grid.gridSize, Space.World);
                    AddToGrid();
                    _grid.CheckForLines();
                    CalculateTime();
                    if (CheckLose())
                    {
                        EventBetter.Raise(new GameOverManager.GameOverEvent());
                    }
                    else
                    {
                        //FindObjectOfType<TetrominoSpawner>().SpawnTetromino();
                    }

                    _tetrominoShadow.DisableShadow();
                    this.enabled = false;
                }
                else
                {
                    _tetrominoShadow.CastShadow(this, _minos);
                }
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.Space))
            {
                var safetyCast = 100;
                while (IsValidMove() && safetyCast > 0)
                {
                    transform.Translate(Vector3.down * _grid.gridSize, Space.World);
                    safetyCast--;
                }

                transform.Translate(Vector3.up * _grid.gridSize, Space.World);
                AddToGrid();
                _grid.CheckForLines();
                if (CheckLose())
                {
                    EventBetter.Raise(new GameOverManager.GameOverEvent());
                }
                else
                {
                    //FindObjectOfType<TetrominoSpawner>().SpawnTetromino();
                }

                _tetrominoShadow.DisableShadow();
                this.enabled = false;
            }
        }


        private void CalculateTime()
        {
            if (lastFallTime == 0)
            {
                lastFallTime = Time.time;
                timeUsed = lastFallTime - startTime;
            }
        }

        private void AddToGrid()
        {
            var effectCenter = Vector3.zero; var index = 0;
            
            foreach (Mino mino in _minos)
            {
                _grid.AddToGrid(new GridMino(mino, this));

                if (index == 0)
                {
                    index++;
                    effectCenter = mino.transform.position;
                }
            }

            _grid.LandedAndCallDecoder(skillName, 0);
            // 新增：每次方块落地时推进敌人回合
            var enemyTurnController = FindObjectOfType<EnemyTurnController>();
            if (enemyTurnController != null)
            {
                enemyTurnController.AdvanceAllEnemiesTurn();
            }
            
            //效果：方块落地时播放
            EventBetter.Raise(new CamShakeRequest("Small"));
            EventBetter.Raise(new BloomRequest("Small"));
            EventBetter.Raise(new ZoomRequest("Small", effectCenter));
        }

        private bool IsValidMove()
        {
            foreach (Mino mino in _minos)
            {
                float x = mino.transform.position.x;
                float y = mino.transform.position.y;

                if (x < _grid.BoardBottomLeft.x || y < _grid.BoardBottomLeft.y || x > _grid.BoardTopRight.x)
                {
                    return false;
                }

                Vector2Int gridPos = _grid.GetGridPos(mino.transform.position);
                var gridMino = _grid.GetGridMino(gridPos);
                // 只有非负面方块（tag!=DamageBlock）才算障碍
                if (gridMino != null && gridMino.mino != null && gridMino.mino.gameObject.tag != "DamageBlock")
                {
                    return false;
                }
            }

            return true;
        }

        private bool CheckLose()
        {
            if (GameOverManager.IsGameOver) return true;
            // 1. 新方块与普通方块重叠则GameOver
            foreach (Mino mino in _minos)
            {
                float x = mino.transform.position.x;
                float y = mino.transform.position.y;
                Vector2Int gridPos = _grid.GetGridPos(mino.transform.position);

                // 边界判定
                if (x < _grid.BoardBottomLeft.x || y < _grid.BoardBottomLeft.y || x > _grid.BoardTopRight.x ||
                    y > _grid.BoardTopRight.y)
                {
                    return true;
                }

                // 检查重叠的格子是否有普通方块（非伤害方块）
                var gridMino = _grid.GetGridMino(gridPos);
                if (gridMino != null && gridMino.mino != null && gridMino.mino.gameObject.tag != "DamageBlock")
                {
                    // 如果该方块Layer为Template则忽略
                    if (gridMino.mino.gameObject.layer == LayerMask.NameToLayer("Template")) continue;
                    return true;
                }
            }

            // 2. 棋盘中普通方块和伤害方块重叠
            for (int x = 0; x < _grid.boardSize.x; x++)
            {
                for (int y = 0; y < _grid.boardSize.y; y++)
                {
                    var gridMino = _grid.GridMinos[x, y];
                    if (gridMino != null && gridMino.mino != null)
                    {
                        // 如果当前位置有普通方块
                        if (gridMino.mino.gameObject.tag != "DamageBlock")
                        {
                            // 如果该方块Layer为Template则忽略
                            if (gridMino.mino.gameObject.layer == LayerMask.NameToLayer("Template")) continue;
                            // 检查同一格是否还有伤害方块（通过transform重叠）
                            Collider2D[] colliders = Physics2D.OverlapPointAll(_grid.GetWorldPos(new Vector2Int(x, y)));
                            foreach (var col in colliders)
                            {
                                if (col.CompareTag("DamageBlock"))
                                {
                                    // 如果伤害方块Layer为Template也忽略
                                    if (col.gameObject.layer == LayerMask.NameToLayer("Template")) continue;
                                    return true;
                                }
                            }
                        }
                    }
                }
            }

            return false;
        }
    }
}