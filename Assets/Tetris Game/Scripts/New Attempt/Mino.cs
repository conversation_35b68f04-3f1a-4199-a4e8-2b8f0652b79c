using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace EOP.Tetris
{
    public class Mino : MonoBehaviour
    {
        public bool HasSticker;
        private SpriteRenderer _spriteRenderer;

        private void Awake()
        {
            _spriteRenderer = GetComponent<SpriteRenderer>();
            if (HasSticker)
            {
                _spriteRenderer.color = Color.black;
            }
            else
            {
                _spriteRenderer.color = Color.white;
            }
        }

        private void Start()
        {
            // Auto-scale to match the grid when spawned
            AutoScaleToGrid();
        }

        public void OnEnable()
        {
            _spriteRenderer.enabled = true;
        }

        private void OnDisable()
        {
            _spriteRenderer.enabled = false;
        }

        public virtual void Trigger()
        {
            if (HasSticker)
            {
                Debug.Log("Triggered Mino With Sticker");
            }
        }

        void OnMouseEnter()
        {
            EventBetter.Raise(new SelectionTooltip.ShowTooltipEvent(gameObject.transform.parent.GetComponent<TetrominoEntity>().skillName));
        }

        void OnMouseExit()
        {
            EventBetter.Raise(new SelectionTooltip.ShowTooltipEvent("hide"));
        }

        public void SetMinosColor(string color)
        {
            if (!HasSticker){
                switch (color)
                {
                    case "Sky":
                        _spriteRenderer.color = Color.cyan;
                        break;
                    case "Navy":
                        _spriteRenderer.color = Color.blue;
                        break;
                    case "Orange":
                        _spriteRenderer.color = new Color(1, 0.5f, 0);
                        break;
                    case "Yellow":
                        _spriteRenderer.color = Color.yellow;
                        break;
                    case "Green":
                        _spriteRenderer.color = Color.green;
                        break;
                    case "Purple":
                        _spriteRenderer.color = new Color(0.5f, 0, 0.5f);
                        break;
                    case "Red":
                        _spriteRenderer.color = Color.red;
                        break;
                    default:
                        break;
                }
            }
            else
            {
                _spriteRenderer.color = Color.black;
            }
        }

        /// <summary>
        /// Automatically scale this mino to match the parent grid's scale
        /// </summary>
        public void AutoScaleToGrid()
        {
            // Find the TetrisGrid in parent hierarchy
            TetrisGrid grid = GetComponentInParent<TetrisGrid>();
            if (grid != null)
            {
                // Scale to match the grid
                transform.localScale = Vector3.one;
                return;
            }

            // If no grid found in parents, try to find any TetrisGrid in the scene
            grid = FindObjectOfType<TetrisGrid>();
            if (grid != null)
            {
                transform.localScale = Vector3.one;
            }
        }

        /// <summary>
        /// Manually scale this mino to a specific size
        /// </summary>
        /// <param name="scale">The scale to apply</param>
        public void SetScale(float scale)
        {
            transform.localScale = Vector3.one * scale;
        }

        /// <summary>
        /// Scale this mino to match a specific grid
        /// </summary>
        /// <param name="grid">The grid to match</param>
        public void ScaleToGrid(TetrisGrid grid)
        {
            if (grid != null)
            {
                transform.localScale = Vector3.one;
            }
        }
    }
}
