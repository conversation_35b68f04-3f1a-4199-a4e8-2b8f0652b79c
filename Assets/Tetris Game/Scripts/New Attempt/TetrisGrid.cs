using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;
using EOP.combat;
using EOP.Combat.Decoders;
using EOP.Skills;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

namespace EOP.Tetris
{
    [Serializable]
    public class GridMino
    {
        public Mino mino;
        public TetrominoEntity tetrominoDependency;
        public bool isDamageBlock;
        public GridMino(Mino mino, TetrominoEntity tetrominoDependency)
        {
            this.mino = mino;
            this.tetrominoDependency = tetrominoDependency;
            this.isDamageBlock = false;
        }

        public GridMino(bool isDamageBlock)
        {
            this.isDamageBlock = isDamageBlock;
        }
    }

    [Serializable]
    public struct GridPosition
    {
        [LabelText("X坐标")] public int x;
        [LabelText("Y坐标")] public int y;

        public GridPosition(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public GridPosition(Vector2Int vector2Int)
        {
            this.x = vector2Int.x;
            this.y = vector2Int.y;
        }

        public Vector2Int ToVector2Int()
        {
            return new Vector2Int(x, y);
        }

        public static implicit operator Vector2Int(GridPosition gridPos)
        {
            return new Vector2Int(gridPos.x, gridPos.y);
        }

        public static implicit operator GridPosition(Vector2Int vector2Int)
        {
            return new GridPosition(vector2Int.x, vector2Int.y);
        }

        public override string ToString()
        {
            return $"({x}, {y})";
        }
    }

    [Serializable]
    public struct GridMinoEntry
    {
        [LabelText("网格位置")] public GridPosition position;
        [LabelText("网格方块")] public GridMino gridMino;

        public GridMinoEntry(GridPosition position, GridMino gridMino)
        {
            this.position = position;
            this.gridMino = gridMino;
        }

        public GridMinoEntry(Vector2Int position, GridMino gridMino)
        {
            this.position = new GridPosition(position);
            this.gridMino = gridMino;
        }
    }

    [Serializable]
    public class SerializableGridMinoData
    {
        [LabelText("网格方块数据")]
        [ListDrawerSettings(ShowIndexLabels = true, ListElementLabelName = "position")]
        public List<GridMinoEntry> gridMinoEntries = new List<GridMinoEntry>();

        public Dictionary<Vector2Int, GridMino> ToDictionary()
        {
            var dictionary = new Dictionary<Vector2Int, GridMino>();
            foreach (var entry in gridMinoEntries)
            {
                if (entry.gridMino != null)
                {
                    dictionary[entry.position.ToVector2Int()] = entry.gridMino;
                }
            }
            return dictionary;
        }

        public void FromDictionary(Dictionary<Vector2Int, GridMino> dictionary)
        {
            gridMinoEntries.Clear();
            foreach (var kvp in dictionary)
            {
                if (kvp.Value != null)
                {
                    gridMinoEntries.Add(new GridMinoEntry(kvp.Key, kvp.Value));
                }
            }
        }

        public void FromArray(GridMino[,] gridArray)
        {
            gridMinoEntries.Clear();
            for (int x = 0; x < gridArray.GetLength(0); x++)
            {
                for (int y = 0; y < gridArray.GetLength(1); y++)
                {
                    if (gridArray[x, y] != null)
                    {
                        gridMinoEntries.Add(new GridMinoEntry(new Vector2Int(x, y), gridArray[x, y]));
                    }
                }
            }
        }

        public void AddEntry(Vector2Int position, GridMino gridMino)
        {
            // 检查是否已存在相同位置的条目
            for (int i = 0; i < gridMinoEntries.Count; i++)
            {
                if (gridMinoEntries[i].position.ToVector2Int() == position)
                {
                    // 更新现有条目
                    gridMinoEntries[i] = new GridMinoEntry(position, gridMino);
                    return;
                }
            }
            // 添加新条目
            gridMinoEntries.Add(new GridMinoEntry(position, gridMino));
        }

        public void RemoveEntry(Vector2Int position)
        {
            for (int i = gridMinoEntries.Count - 1; i >= 0; i--)
            {
                if (gridMinoEntries[i].position.ToVector2Int() == position)
                {
                    gridMinoEntries.RemoveAt(i);
                    break;
                }
            }
        }

        public GridMino GetGridMino(Vector2Int position)
        {
            foreach (var entry in gridMinoEntries)
            {
                if (entry.position.ToVector2Int() == position)
                {
                    return entry.gridMino;
                }
            }
            return null;
        }

        public bool ContainsPosition(Vector2Int position)
        {
            return gridMinoEntries.Any(entry => entry.position.ToVector2Int() == position);
        }

        public void Clear()
        {
            gridMinoEntries.Clear();
        }

        public int Count => gridMinoEntries.Count;
    }

    public class TetrisGrid : MonoBehaviour
    {
        [Range(0, 2)]
        [OnValueChanged("OnGridSizeChanged")]
        [LabelText("Grid Size")]
        [InfoBox("Changes to this value will automatically update the grid and scale all minos")]
        public float gridSize;

        public Vector2Int boardSize = new Vector2Int(10, 20);
        [ReadOnly, ShowInInspector] public GridMino[,] GridMinos;

        [FoldoutGroup("可序列化网格数据")]
        [LabelText("网格方块数据 (Inspector可见)")]
        [InfoBox("这个字段显示当前网格中所有非空的方块数据，可以在Inspector中查看和编辑")]
        public SerializableGridMinoData serializableGridData = new SerializableGridMinoData();

        private SpriteRenderer _spriteRenderer;
        private Vector2Int _initialBoardSize;
        private float _previousGridSize;
        
        List<GridMino> _deletionInvolvedGridMinos = new List<GridMino>();
        Dictionary<TetrominoEntity, List<Mino>> _tetrominosToBeTriggeredDictionary = new Dictionary<TetrominoEntity, List<Mino>>();
        
        public Vector2 BoardCenter
        {
            get { return transform.position; }
        }

        [ShowInInspector]
        public Vector2 BoardBottomLeft
        {
            get { return BoardCenter - ((Vector2)boardSize * gridSize) / 2; }
        }

        [ShowInInspector]
        public Vector2 BoardTopRight
        {
            get { return BoardCenter + ((Vector2)boardSize * gridSize) / 2; }
        }

        private SkillDecoder _Decoder;
        private SkillStorage _skillStorage;

        protected virtual void Awake()
        {
            _spriteRenderer = GetComponent<SpriteRenderer>();
            _spriteRenderer.size = boardSize;
            GridMinos = new GridMino[boardSize.x, boardSize.y];
            _initialBoardSize = boardSize;
            _previousGridSize = gridSize;
        }

        private void Start()
        {
            UpdateGridScale();
            _Decoder = FindObjectOfType<SkillDecoder>();
            _skillStorage = FindObjectOfType<SkillStorage>();
        }

#if UNITY_EDITOR
        /// <summary>
        /// Called when values change in the inspector (Editor only)
        /// </summary>
        private void OnValidate()
        {
            // Only update if gridSize actually changed
            if (Mathf.Abs(_previousGridSize - gridSize) > 0.001f)
            {
                OnGridSizeChanged();
                _previousGridSize = gridSize;
            }
        }
#endif

        /// <summary>
        /// Called when gridSize changes via Odin Inspector or OnValidate
        /// </summary>
        private void OnGridSizeChanged()
        {
#if UNITY_EDITOR
            if (Application.isPlaying || EditorApplication.isPlaying)
#else
            if (Application.isPlaying)
#endif
            {
                // Runtime update
                UpdateGridScale();
                ScaleAllExistingMinos();
            }
#if UNITY_EDITOR
            else
            {
                // Editor-time update
                UpdateGridScaleInEditor();
            }
#endif
        }

        /// <summary>
        /// Update grid scale during runtime
        /// </summary>
        private void UpdateGridScale()
        {
            if (_spriteRenderer != null)
            {
                transform.localScale = Vector3.one * gridSize;
            }
        }

        /// <summary>
        /// Update grid scale in editor mode
        /// </summary>
        private void UpdateGridScaleInEditor()
        {
#if UNITY_EDITOR
            if (_spriteRenderer == null)
                _spriteRenderer = GetComponent<SpriteRenderer>();

            if (_spriteRenderer != null)
            {
                transform.localScale = Vector3.one * gridSize;

                // Mark the scene as dirty so changes are saved
                if (!Application.isPlaying)
                {
                    EditorUtility.SetDirty(this);
                    EditorSceneManager.MarkSceneDirty(gameObject.scene);
                }
            }
#endif
        }

        public void AddToGrid(GridMino gridMino)
        {
            var gridPos = GetGridPos(gridMino.mino.transform.position);
            // if out of bound
            if (gridPos.y >= GridMinos.GetLength(1))
            {
                EventBetter.Raise(new GameOverManager.GameOverEvent());
            }
            else if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 &&
                gridPos.y >= 0)
            {
                GridMinos[gridPos.x, gridPos.y] = gridMino;
                // 同步到可序列化数据
                serializableGridData.AddEntry(gridPos, gridMino);

                // Scale the newly added mino to match current grid size
                ScaleMino(gridMino.mino);
            }
        }

        /// <summary>
        /// Scale all existing minos in the grid to match current grid size
        /// </summary>
        private void ScaleAllExistingMinos()
        {
            if (GridMinos == null) return;

            for (int x = 0; x < GridMinos.GetLength(0); x++)
            {
                for (int y = 0; y < GridMinos.GetLength(1); y++)
                {
                    if (GridMinos[x, y] != null && GridMinos[x, y].mino != null)
                    {
                        ScaleMino(GridMinos[x, y].mino);
                    }
                }
            }

            // Also scale any child minos that might not be in the grid array yet
            ScaleChildMinos();
        }

        /// <summary>
        /// Scale a specific mino to match the current grid size
        /// </summary>
        private void ScaleMino(Mino mino)
        {
            if (mino != null)
            {
                // Scale the mino to match the grid size
                // The mino should be scaled relative to the grid's scale
                mino.transform.localScale = Vector3.one;
            }
        }

        /// <summary>
        /// Scale all child minos (including those in TetrominoEntities)
        /// </summary>
        private void ScaleChildMinos()
        {
            // Find all Mino components in children
            Mino[] allMinos = GetComponentsInChildren<Mino>();
            foreach (Mino mino in allMinos)
            {
                ScaleMino(mino);
            }

            // Find all TetrominoEntity components and scale their minos
            TetrominoEntity[] tetrominoEntities = GetComponentsInChildren<TetrominoEntity>();
            foreach (TetrominoEntity entity in tetrominoEntities)
            {
                Mino[] entityMinos = entity.GetComponentsInChildren<Mino>();
                foreach (Mino mino in entityMinos)
                {
                    ScaleMino(mino);
                }
            }
        }

        /// <summary>
        /// 将当前网格数组同步到可序列化数据
        /// </summary>
        [Button("同步网格数据到Inspector")]
        [FoldoutGroup("可序列化网格数据")]
        public void SyncGridToSerializableData()
        {
            serializableGridData.FromArray(GridMinos);
        }

        /// <summary>
        /// 从可序列化数据恢复到网格数组
        /// </summary>
        [Button("从Inspector数据恢复网格")]
        [FoldoutGroup("可序列化网格数据")]
        public void SyncSerializableDataToGrid()
        {
            // 清空当前网格
            GridMinos = new GridMino[boardSize.x, boardSize.y];

            // 从可序列化数据恢复
            foreach (var entry in serializableGridData.gridMinoEntries)
            {
                var pos = entry.position.ToVector2Int();
                if (pos.x >= 0 && pos.x < boardSize.x && pos.y >= 0 && pos.y < boardSize.y)
                {
                    GridMinos[pos.x, pos.y] = entry.gridMino;
                }
            }
        }


        public void LandedAndCallDecoder(string skillName, float timeUsed)
        {
            _Decoder.timeTakenToDrop = timeUsed;
            //_Decoder.PieceDecode(pieceName);
            _Decoder.LandDecode(skillName);
        }

        public void LineClearedAndAction(string skillName, int clearNumber)
        {
            //_Decoder.ActionDecode(pieceName, clearNumber);
            _Decoder.ActionDecode(skillName, clearNumber);
        }

        public void CheckForLines()
        {
            _deletionInvolvedGridMinos.Clear();
            _tetrominosToBeTriggeredDictionary.Clear();
            int numLinesDeleted = 0;
            // Check for lines to clear
            for (int y = boardSize.y - 1; y >= 0; y--)
            {
                if (HasLine(y))
                {
                    DeleteLine(y);
                    numLinesDeleted++;
                    RunDown(y);
                }
            }
            
            // If deleted line(s), we calculate the strength of each TetrominoEntity to be triggered
            if (_deletionInvolvedGridMinos.Count > 0)
            {
                foreach (var gridMino in _deletionInvolvedGridMinos)
                {
                    if (_tetrominosToBeTriggeredDictionary.ContainsKey(gridMino.tetrominoDependency))
                    {
                        _tetrominosToBeTriggeredDictionary[gridMino.tetrominoDependency].Add(gridMino.mino);
                    }
                    else
                    {
                        _tetrominosToBeTriggeredDictionary.Add(gridMino.tetrominoDependency, new List<Mino>(){gridMino.mino});
                    }
                }
            }

            // If deleted line(s), we trigger the action of the ClearLine
            for (int i = 0; i < numLinesDeleted; i++)
            {
                _Decoder.ClearLine();
            }
            
            // Then we trigger effects of minos on dependent TetrominoEntity and trigger sticker effects
            foreach (var tetrominosToBeTriggered in _tetrominosToBeTriggeredDictionary)
            {
                tetrominosToBeTriggered.Key.Trigger(tetrominosToBeTriggered.Value);
                for (int i = tetrominosToBeTriggered.Value.Count - 1; i >= 0; i--)
                {
                    tetrominosToBeTriggered.Value[i].transform.parent = null;
                    Destroy(tetrominosToBeTriggered.Value[i].gameObject);
                    
                    if (tetrominosToBeTriggered.Key.transform.childCount == 1)
                    {
                        Destroy(tetrominosToBeTriggered.Key.gameObject);
                    }
                }
            }
        }

        bool HasLine(int y)
        {
            for (int x = 0; x < boardSize.x; x++)
            {
                if (GridMinos[x, y] == null || GridMinos[x, y].isDamageBlock)
                {
                    return false;
                }
            }

            return true;
        }

        void DeleteLine(int y)
        {
            for (int x = 0; x < boardSize.x; x++)
            {
                _deletionInvolvedGridMinos.Add(GridMinos[x, y]);
                GridMinos[x, y] = null;
                // 同步删除可序列化数据
                serializableGridData.RemoveEntry(new Vector2Int(x, y));
            }
        }

        void RunDown(int y)
        {
            for (int vY = y; vY < boardSize.y; vY++)
            {
                for (int x = 0; x < boardSize.x; x++)
                {
                    if (GridMinos[x, vY] != null && GridMinos[x, vY].isDamageBlock == false)
                    {
                        GridMinos[x, vY - 1] = GridMinos[x, vY];
                        GridMinos[x, vY] = null;
                        GridMinos[x, vY - 1].mino.transform.Translate(Vector3.down * gridSize, Space.World);

                        // Ensure the moved mino maintains proper scale
                        ScaleMino(GridMinos[x, vY - 1].mino);

                        // 同步更新可序列化数据
                        var oldPos = new Vector2Int(x, vY);
                        var newPos = new Vector2Int(x, vY - 1);
                        var gridMino = GridMinos[x, vY - 1];

                        serializableGridData.RemoveEntry(oldPos);
                        serializableGridData.AddEntry(newPos, gridMino);
                    }
                }
            }
        }
        
        public bool DeleteTopRow()
        {
            if (GridMinos.GetLength(1) > 1)
            {
                GridMino[,] newGridMinos = new GridMino[boardSize.x, boardSize.y - 1];
                for (int x = 0; x < GridMinos.GetLength(0); x++)
                {
                    for (int y = 0; y < GridMinos.GetLength(1) - 1; y++)
                    {
                        newGridMinos[x, y] = GridMinos[x, y];
                    }
                }
                
                boardSize = new Vector2Int(boardSize.x, boardSize.y - 1);
                
                // Update board and align it
                _spriteRenderer.size = boardSize;
                this.transform.Translate(Vector3.down * gridSize * 0.5f, Space.World);
                
                // Allign the child objects
                for (int i = 0; i < transform.childCount; i++)
                {
                    transform.GetChild(i).Translate(Vector3.up * gridSize * 0.5f, Space.World);
                }
                
                // Update gridMinos
                GridMinos = newGridMinos;

                return true;
            }
            else
            {
               
            }
            
            return false;
        }
        public bool AddRowToTop()
        {
            if (GridMinos.GetLength(1) < _initialBoardSize.y)
            {
                GridMino[,] newGridMinos = new GridMino[boardSize.x, boardSize.y + 1];
                for (int x = 0; x < GridMinos.GetLength(0); x++)
                {
                    for (int y = 0; y < GridMinos.GetLength(1); y++)
                    {
                        newGridMinos[x, y] = GridMinos[x, y];
                    }
                }
                
                boardSize = new Vector2Int(boardSize.x, boardSize.y + 1);
                
                // Update board and align it
                _spriteRenderer.size = boardSize;
                this.transform.Translate(Vector3.up * gridSize * 0.5f, Space.World);
                
                // Update gridMinos
                GridMinos = newGridMinos;

                return true;
            }
            else
            {
            }
            
            return false;
        }

        public GridMino GetGridMino(Vector2Int gridPos)
        {
            if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 &&
                gridPos.y >= 0)
            {
                return GridMinos[gridPos.x, gridPos.y];
            }

            return null;
        }

        public Vector2Int GetGridPos(Vector2 worldPos)
        {
            var gridPos = (worldPos - BoardBottomLeft - Vector2.one * gridSize / 2) / gridSize;
            return new Vector2Int(Mathf.RoundToInt(gridPos.x), Mathf.RoundToInt(gridPos.y));
        }

        public Vector3 GetWorldPos(Vector2Int gridPos)
        {
            return (Vector2)gridPos * gridSize + BoardBottomLeft + Vector2.one * gridSize / 2;
        }

        // 新增：专门用于负面方块的占位
        public void AddDamageBlockToGrid(Vector2Int gridPos, GameObject block)
        {
            if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 && gridPos.y >= 0)
            {
                // 用GridMino的mino字段存null，或可扩展GridMino结构
                var damageGridMino = new GridMino(true);
                GridMinos[gridPos.x, gridPos.y] = damageGridMino;
                // 同步到可序列化数据
                serializableGridData.AddEntry(gridPos, damageGridMino);
                // 你也可以扩展GridMino加GameObject字段来存block
            }
        }

        #region Testing and Debug Methods

        [FoldoutGroup("Grid Scaling Testing")]
        [Button("Force Update Grid Scale", ButtonSizes.Medium)]
        [InfoBox("Manually update grid scale and all mino scales")]
        public void ForceUpdateGridScale()
        {
            UpdateGridScale();
            ScaleAllExistingMinos();
            Debug.Log($"Updated grid scale to {gridSize} and scaled all minos");
        }

        [FoldoutGroup("Grid Scaling Testing")]
        [Button("Scale All Minos", ButtonSizes.Medium)]
        [InfoBox("Scale all existing minos to match current grid size")]
        public void ForceScaleAllMinos()
        {
            ScaleAllExistingMinos();
            Debug.Log($"Scaled all minos to match grid size {gridSize}");
        }

        [FoldoutGroup("Grid Scaling Testing")]
        [Button("Count All Minos", ButtonSizes.Medium)]
        [InfoBox("Count and log all minos in the scene")]
        public void CountAllMinos()
        {
            int gridMinoCount = 0;
            int childMinoCount = 0;
            int tetrominoMinoCount = 0;

            // Count minos in grid
            if (GridMinos != null)
            {
                for (int x = 0; x < GridMinos.GetLength(0); x++)
                {
                    for (int y = 0; y < GridMinos.GetLength(1); y++)
                    {
                        if (GridMinos[x, y] != null && GridMinos[x, y].mino != null)
                        {
                            gridMinoCount++;
                        }
                    }
                }
            }

            // Count child minos
            Mino[] allMinos = GetComponentsInChildren<Mino>();
            childMinoCount = allMinos.Length;

            // Count tetromino minos
            TetrominoEntity[] tetrominoEntities = GetComponentsInChildren<TetrominoEntity>();
            foreach (TetrominoEntity entity in tetrominoEntities)
            {
                Mino[] entityMinos = entity.GetComponentsInChildren<Mino>();
                tetrominoMinoCount += entityMinos.Length;
            }

            Debug.Log($"Mino Count - Grid: {gridMinoCount}, Children: {childMinoCount}, Tetromino: {tetrominoMinoCount}");
        }

        [FoldoutGroup("Grid Scaling Testing")]
        [Button("Test Grid Size Change", ButtonSizes.Medium)]
        [InfoBox("Test changing grid size programmatically")]
        public void TestGridSizeChange()
        {
            float newSize = gridSize == 1f ? 0.5f : 1f;
            gridSize = newSize;
            OnGridSizeChanged();
            Debug.Log($"Changed grid size to {gridSize}");
        }

        #endregion
    }
}