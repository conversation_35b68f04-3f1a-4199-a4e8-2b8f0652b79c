using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;
using EOP.combat;
using EOP.Combat.Decoders;
using EOP.Skills;

namespace EOP.Tetris
{
    [Serializable]
    public class GridMino
    {
        public Mino mino;
        public TetrominoEntity tetrominoDependency;
        public bool isDamageBlock;
        public GridMino(Mino mino, TetrominoEntity tetrominoDependency)
        {
            this.mino = mino;
            this.tetrominoDependency = tetrominoDependency;
            this.isDamageBlock = false;
        }

        public GridMino(bool isDamageBlock)
        {
            this.isDamageBlock = isDamageBlock;
        }
    }

    [Serializable]
    public struct GridPosition
    {
        [LabelText("X坐标")] public int x;
        [LabelText("Y坐标")] public int y;

        public GridPosition(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public GridPosition(Vector2Int vector2Int)
        {
            this.x = vector2Int.x;
            this.y = vector2Int.y;
        }

        public Vector2Int ToVector2Int()
        {
            return new Vector2Int(x, y);
        }

        public static implicit operator Vector2Int(GridPosition gridPos)
        {
            return new Vector2Int(gridPos.x, gridPos.y);
        }

        public static implicit operator GridPosition(Vector2Int vector2Int)
        {
            return new GridPosition(vector2Int.x, vector2Int.y);
        }

        public override string ToString()
        {
            return $"({x}, {y})";
        }
    }

    [Serializable]
    public struct GridMinoEntry
    {
        [LabelText("网格位置")] public GridPosition position;
        [LabelText("网格方块")] public GridMino gridMino;

        public GridMinoEntry(GridPosition position, GridMino gridMino)
        {
            this.position = position;
            this.gridMino = gridMino;
        }

        public GridMinoEntry(Vector2Int position, GridMino gridMino)
        {
            this.position = new GridPosition(position);
            this.gridMino = gridMino;
        }
    }

    [Serializable]
    public class SerializableGridMinoData
    {
        [LabelText("网格方块数据")]
        [ListDrawerSettings(ShowIndexLabels = true, ListElementLabelName = "position")]
        public List<GridMinoEntry> gridMinoEntries = new List<GridMinoEntry>();

        public Dictionary<Vector2Int, GridMino> ToDictionary()
        {
            var dictionary = new Dictionary<Vector2Int, GridMino>();
            foreach (var entry in gridMinoEntries)
            {
                if (entry.gridMino != null)
                {
                    dictionary[entry.position.ToVector2Int()] = entry.gridMino;
                }
            }
            return dictionary;
        }

        public void FromDictionary(Dictionary<Vector2Int, GridMino> dictionary)
        {
            gridMinoEntries.Clear();
            foreach (var kvp in dictionary)
            {
                if (kvp.Value != null)
                {
                    gridMinoEntries.Add(new GridMinoEntry(kvp.Key, kvp.Value));
                }
            }
        }

        public void FromArray(GridMino[,] gridArray)
        {
            gridMinoEntries.Clear();
            for (int x = 0; x < gridArray.GetLength(0); x++)
            {
                for (int y = 0; y < gridArray.GetLength(1); y++)
                {
                    if (gridArray[x, y] != null)
                    {
                        gridMinoEntries.Add(new GridMinoEntry(new Vector2Int(x, y), gridArray[x, y]));
                    }
                }
            }
        }

        public void AddEntry(Vector2Int position, GridMino gridMino)
        {
            // 检查是否已存在相同位置的条目
            for (int i = 0; i < gridMinoEntries.Count; i++)
            {
                if (gridMinoEntries[i].position.ToVector2Int() == position)
                {
                    // 更新现有条目
                    gridMinoEntries[i] = new GridMinoEntry(position, gridMino);
                    return;
                }
            }
            // 添加新条目
            gridMinoEntries.Add(new GridMinoEntry(position, gridMino));
        }

        public void RemoveEntry(Vector2Int position)
        {
            for (int i = gridMinoEntries.Count - 1; i >= 0; i--)
            {
                if (gridMinoEntries[i].position.ToVector2Int() == position)
                {
                    gridMinoEntries.RemoveAt(i);
                    break;
                }
            }
        }

        public GridMino GetGridMino(Vector2Int position)
        {
            foreach (var entry in gridMinoEntries)
            {
                if (entry.position.ToVector2Int() == position)
                {
                    return entry.gridMino;
                }
            }
            return null;
        }

        public bool ContainsPosition(Vector2Int position)
        {
            return gridMinoEntries.Any(entry => entry.position.ToVector2Int() == position);
        }

        public void Clear()
        {
            gridMinoEntries.Clear();
        }

        public int Count => gridMinoEntries.Count;
    }

    public class TetrisGrid : MonoBehaviour
    {
        [Range(0, 2)] public float gridSize;
        public Vector2Int boardSize = new Vector2Int(10, 20);
        [ReadOnly, ShowInInspector] public GridMino[,] GridMinos;

        [FoldoutGroup("可序列化网格数据")]
        [LabelText("网格方块数据 (Inspector可见)")]
        [InfoBox("这个字段显示当前网格中所有非空的方块数据，可以在Inspector中查看和编辑")]
        public SerializableGridMinoData serializableGridData = new SerializableGridMinoData();

        private SpriteRenderer _spriteRenderer;
        private Vector2Int _initialBoardSize;
        
        List<GridMino> _deletionInvolvedGridMinos = new List<GridMino>();
        Dictionary<TetrominoEntity, List<Mino>> _tetrominosToBeTriggeredDictionary = new Dictionary<TetrominoEntity, List<Mino>>();
        
        public Vector2 BoardCenter
        {
            get { return transform.position; }
        }

        [ShowInInspector]
        public Vector2 BoardBottomLeft
        {
            get { return BoardCenter - ((Vector2)boardSize * gridSize) / 2; }
        }

        [ShowInInspector]
        public Vector2 BoardTopRight
        {
            get { return BoardCenter + ((Vector2)boardSize * gridSize) / 2; }
        }

        private SkillDecoder _Decoder;
        private SkillStorage _skillStorage;

        protected virtual void Awake()
        {
            _spriteRenderer = GetComponent<SpriteRenderer>();
            _spriteRenderer.size = boardSize;
            GridMinos = new GridMino[boardSize.x, boardSize.y];
            _initialBoardSize = boardSize;
        }

        private void Start()
        {
            transform.localScale *= gridSize;
            _Decoder = FindObjectOfType<SkillDecoder>();
            _skillStorage = FindObjectOfType<SkillStorage>();
        }

        public void AddToGrid(GridMino gridMino)
        {
            var gridPos = GetGridPos(gridMino.mino.transform.position);
            if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 &&
                gridPos.y >= 0)
            {
                GridMinos[gridPos.x, gridPos.y] = gridMino;
                // 同步到可序列化数据
                serializableGridData.AddEntry(gridPos, gridMino);
            }
        }

        /// <summary>
        /// 将当前网格数组同步到可序列化数据
        /// </summary>
        [Button("同步网格数据到Inspector")]
        [FoldoutGroup("可序列化网格数据")]
        public void SyncGridToSerializableData()
        {
            serializableGridData.FromArray(GridMinos);
        }

        /// <summary>
        /// 从可序列化数据恢复到网格数组
        /// </summary>
        [Button("从Inspector数据恢复网格")]
        [FoldoutGroup("可序列化网格数据")]
        public void SyncSerializableDataToGrid()
        {
            // 清空当前网格
            GridMinos = new GridMino[boardSize.x, boardSize.y];

            // 从可序列化数据恢复
            foreach (var entry in serializableGridData.gridMinoEntries)
            {
                var pos = entry.position.ToVector2Int();
                if (pos.x >= 0 && pos.x < boardSize.x && pos.y >= 0 && pos.y < boardSize.y)
                {
                    GridMinos[pos.x, pos.y] = entry.gridMino;
                }
            }
        }


        public void LandedAndCallDecoder(string skillName, float timeUsed)
        {
            _Decoder.timeTakenToDrop = timeUsed;
            //_Decoder.PieceDecode(pieceName);
            _Decoder.LandDecode(skillName);
        }

        public void LineClearedAndAction(string skillName, int clearNumber)
        {
            //_Decoder.ActionDecode(pieceName, clearNumber);
            _Decoder.ActionDecode(skillName, clearNumber);
        }

        public void CheckForLines()
        {
            _deletionInvolvedGridMinos.Clear();
            _tetrominosToBeTriggeredDictionary.Clear();
            int numLinesDeleted = 0;
            // Check for lines to clear
            for (int y = boardSize.y - 1; y >= 0; y--)
            {
                if (HasLine(y))
                {
                    DeleteLine(y);
                    numLinesDeleted++;
                    RunDown(y);
                }
            }
            
            // If deleted line(s), we calculate the strength of each TetrominoEntity to be triggered
            if (_deletionInvolvedGridMinos.Count > 0)
            {
                foreach (var gridMino in _deletionInvolvedGridMinos)
                {
                    if (_tetrominosToBeTriggeredDictionary.ContainsKey(gridMino.tetrominoDependency))
                    {
                        _tetrominosToBeTriggeredDictionary[gridMino.tetrominoDependency].Add(gridMino.mino);
                    }
                    else
                    {
                        _tetrominosToBeTriggeredDictionary.Add(gridMino.tetrominoDependency, new List<Mino>(){gridMino.mino});
                    }
                }
            }

            // If deleted line(s), we trigger the action of the ClearLine
            for (int i = 0; i < numLinesDeleted; i++)
            {
                _Decoder.ClearLine();
            }
            
            // Then we trigger effects of minos on dependent TetrominoEntity and trigger sticker effects
            foreach (var tetrominosToBeTriggered in _tetrominosToBeTriggeredDictionary)
            {
                tetrominosToBeTriggered.Key.Trigger(tetrominosToBeTriggered.Value);
                for (int i = tetrominosToBeTriggered.Value.Count - 1; i >= 0; i--)
                {
                    tetrominosToBeTriggered.Value[i].transform.parent = null;
                    Destroy(tetrominosToBeTriggered.Value[i].gameObject);
                    
                    if (tetrominosToBeTriggered.Key.transform.childCount == 1)
                    {
                        Destroy(tetrominosToBeTriggered.Key.gameObject);
                    }
                }
            }
        }

        bool HasLine(int y)
        {
            for (int x = 0; x < boardSize.x; x++)
            {
                if (GridMinos[x, y] == null || GridMinos[x, y].isDamageBlock)
                {
                    return false;
                }
            }

            return true;
        }

        void DeleteLine(int y)
        {
            for (int x = 0; x < boardSize.x; x++)
            {
                _deletionInvolvedGridMinos.Add(GridMinos[x, y]);
                GridMinos[x, y] = null;
                // 同步删除可序列化数据
                serializableGridData.RemoveEntry(new Vector2Int(x, y));
            }
        }

        void RunDown(int y)
        {
            for (int vY = y; vY < boardSize.y; vY++)
            {
                for (int x = 0; x < boardSize.x; x++)
                {
                    if (GridMinos[x, vY] != null && GridMinos[x, vY].isDamageBlock == false)
                    {
                        GridMinos[x, vY - 1] = GridMinos[x, vY];
                        GridMinos[x, vY] = null;
                        GridMinos[x, vY - 1].mino.transform.Translate(Vector3.down * gridSize, Space.World);

                        // 同步更新可序列化数据
                        var oldPos = new Vector2Int(x, vY);
                        var newPos = new Vector2Int(x, vY - 1);
                        var gridMino = GridMinos[x, vY - 1];

                        serializableGridData.RemoveEntry(oldPos);
                        serializableGridData.AddEntry(newPos, gridMino);
                    }
                }
            }
        }
        
        public bool DeleteTopRow()
        {
            if (GridMinos.GetLength(1) > 1)
            {
                GridMino[,] newGridMinos = new GridMino[boardSize.x, boardSize.y - 1];
                for (int x = 0; x < GridMinos.GetLength(0); x++)
                {
                    for (int y = 0; y < GridMinos.GetLength(1) - 1; y++)
                    {
                        newGridMinos[x, y] = GridMinos[x, y];
                    }
                }
                
                boardSize = new Vector2Int(boardSize.x, boardSize.y - 1);
                
                // Update board and align it
                _spriteRenderer.size = boardSize;
                this.transform.Translate(Vector3.down * gridSize * 0.5f, Space.World);
                
                // Allign the child objects
                for (int i = 0; i < transform.childCount; i++)
                {
                    transform.GetChild(i).Translate(Vector3.up * gridSize * 0.5f, Space.World);
                }
                
                // Update gridMinos
                GridMinos = newGridMinos;

                return true;
            }
            else
            {
                EventBetter.Raise(new GameOverManager.GameOverEvent());
            }
            
            return false;
        }
        public bool AddRowToTop()
        {
            if (GridMinos.GetLength(1) < _initialBoardSize.y)
            {
                GridMino[,] newGridMinos = new GridMino[boardSize.x, boardSize.y + 1];
                for (int x = 0; x < GridMinos.GetLength(0); x++)
                {
                    for (int y = 0; y < GridMinos.GetLength(1); y++)
                    {
                        newGridMinos[x, y] = GridMinos[x, y];
                    }
                }
                
                boardSize = new Vector2Int(boardSize.x, boardSize.y + 1);
                
                // Update board and align it
                _spriteRenderer.size = boardSize;
                this.transform.Translate(Vector3.up * gridSize * 0.5f, Space.World);
                
                // Update gridMinos
                GridMinos = newGridMinos;

                return true;
            }
            else
            {
            }
            
            return false;
        }

        public GridMino GetGridMino(Vector2Int gridPos)
        {
            if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 &&
                gridPos.y >= 0)
            {
                return GridMinos[gridPos.x, gridPos.y];
            }

            return null;
        }

        public Vector2Int GetGridPos(Vector2 worldPos)
        {
            var gridPos = (worldPos - BoardBottomLeft - Vector2.one * gridSize / 2) / gridSize;
            return new Vector2Int(Mathf.RoundToInt(gridPos.x), Mathf.RoundToInt(gridPos.y));
        }

        public Vector3 GetWorldPos(Vector2Int gridPos)
        {
            return (Vector2)gridPos * gridSize + BoardBottomLeft + Vector2.one * gridSize / 2;
        }

        // 新增：专门用于负面方块的占位
        public void AddDamageBlockToGrid(Vector2Int gridPos, GameObject block)
        {
            if (gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 && gridPos.y >= 0)
            {
                // 用GridMino的mino字段存null，或可扩展GridMino结构
                var damageGridMino = new GridMino(true);
                GridMinos[gridPos.x, gridPos.y] = damageGridMino;
                // 同步到可序列化数据
                serializableGridData.AddEntry(gridPos, damageGridMino);
                // 你也可以扩展GridMino加GameObject字段来存block
            }
        }
    }
}