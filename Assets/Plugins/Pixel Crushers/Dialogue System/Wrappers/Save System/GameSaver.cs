// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    //[AddComponentMenu("Pixel Crushers/Dialogue System/Save System/Game Saver")]
    //--- Deprecated in favor of Pixel Crushers Save System.
    public class GameSaver : PixelCrushers.DialogueSystem.GameSaver
    {
    }

}
