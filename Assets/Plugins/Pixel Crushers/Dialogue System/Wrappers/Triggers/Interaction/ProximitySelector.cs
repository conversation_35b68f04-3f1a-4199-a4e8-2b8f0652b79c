// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [HelpURL("http://www.pixelcrushers.com/dialogue_system/manual2x/html/proximity_selector.html")]
    [AddComponentMenu("Pixel Crushers/Dialogue System/Actor/Player/Proximity Selector")]
    public class ProximitySelector : PixelCrushers.DialogueSystem.ProximitySelector
    {
    }

}
