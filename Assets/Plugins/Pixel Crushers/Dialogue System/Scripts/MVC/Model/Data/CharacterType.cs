// Copyright (c) Pixel Crushers. All rights reserved.

namespace PixelCrushers.DialogueSystem
{

    /// <summary>
    /// Specifies whether a character is a player character (PC) or non-player character (NPC).
    /// </summary>
    public enum CharacterType
    {

        /// <summary>
        /// Player character
        /// </summary>
        PC,

        /// <summary>
        /// Non-player character
        /// </summary>
        NPC
    }

}
