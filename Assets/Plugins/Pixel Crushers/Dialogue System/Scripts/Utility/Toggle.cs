// Copyright (c) Pixel Crushers. All rights reserved.

namespace PixelCrushers.DialogueSystem
{

    /// <summary>
    /// Specifies how to set a Boolean value. Flip indicates to invert the current value.
    /// </summary>
    public enum Toggle
    {

        /// <summary>
        /// Set Boolean to true
        /// </summary>
        True,

        /// <summary>
        /// Set boolean to false
        /// </summary>
        False,

        /// <summary>
        /// Invert the current value
        /// </summary>
        Flip
    }

}
