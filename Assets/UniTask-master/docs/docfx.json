{"metadata": [{"src": [{"files": ["UniTask/Assets/Plugins/UniTask/Runtime/**/*.cs"], "src": "../src"}], "dest": "api", "disableGitFeatures": false, "disableDefaultFilter": false}], "build": {"globalMetadata": {"_disableContribution": true, "_appTitle": "UniTask"}, "content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["_DocfxTemplate/templates/default-v2.5.2", "_DocfxTemplate/templates/cysharp"], "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false}}