using Cysharp.Threading.Tasks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;

public class FooMonoBehaviour : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {

    }

    //private async UniTask Download(UnityWebRequest req, string filePath)
    //{
    //    _ = req.SendWebRequest();




    //    // var aaa = await foo;
    //    // Debug.Log(aaa);
    //    await UniTask.Yield();
    //    //File.WriteAllText(filePath, req.downloadHandler.text ?? string.Empty);
    //}
}
