using System.Collections.Generic;
using UnityEngine;
using EOP.combat;
using EOP.Enemy;

namespace EOP.Enemy
{
    /// <summary>
    /// 敌人生成器和技能管理器
    /// 负责从EnemyDatabase生成敌人并管理它们的技能系统
    /// </summary>
    public class EnemyGenerator : MonoBehaviour
    {
        [Header("敌人数据库")]
        public EnemyDatabase enemyDatabase;
        
        [Header("敌人预制体映射")]
        [Toolt<PERSON>("小偷大鹅的预制体")]
        public GameObject thiefGoosePrefab;
        [Tooltip("面具疯子的预制体")]
        public GameObject maskedManiacPrefab;
        [Tooltip("基础敌人预制体（默认）")]
        public GameObject basicEnemyPrefab;
        
        [Header("显示预制体（敌人的大图）映射")]
        [Tooltip("小偷大鹅的显示预制体")]
        public GameObject thiefGooseDisplayPrefab;
        [Tooltip("面具疯子的显示预制体")]
        public GameObject maskedManiacDisplayPrefab;
        [Tooltip("基础敌人的显示预制体")]
        public GameObject basicEnemyDisplayPrefab;
        
        [Header("显示预制体（敌人的大图）设置")]
        [Tooltip("三个预设位置用于初始化显示预制体")]
        public Vector3[] displayPositions = new Vector3[3] 
        {
            new Vector3(-3, 0, 0),
            new Vector3(0, 0, 0), 
            new Vector3(3, 0, 0)
        };
        
        [Tooltip("要显示的敌人类型列表")]
        public string[] displayEnemyTypes = new string[3] 
        {
            "小偷大鹅",
            "面具疯子", 
            "小偷大鹅" // 可以重复
        };
        
        [Tooltip("三个显示预制体的大小设置")]
        public Vector3[] displayScales = new Vector3[3] 
        {
            Vector3.one,
            Vector3.one,
            Vector3.one
        };
        
        [Tooltip("是否在Start时自动初始化显示预制体")]
        public bool autoInitializeDisplayPrefabs = true;
        
        private CombatManager combatManager;
        private List<GameObject> displayPrefabs = new List<GameObject>();
        
        // 存储战斗敌人与显示预制体的对应关系
        private Dictionary<CombatEnemyInterface, GameObject> enemyToDisplayPrefabMap = new Dictionary<CombatEnemyInterface, GameObject>();
        
        void Awake()
        {
            combatManager = FindObjectOfType<CombatManager>();
            if (combatManager == null)
            {
                Debug.LogError("CombatManager not found in scene!");
            }
        }
        
        void Start()
        {
            if (autoInitializeDisplayPrefabs)
            {
                InitializeDisplayPrefabs();
            }
        }
        
        /// <summary>
        /// 根据敌人名称获取对应的战斗预制体
        /// </summary>
        private GameObject GetEnemyPrefab(string enemyName)
        {
            switch (enemyName)
            {
                case "小偷大鹅":
                    if (thiefGoosePrefab != null)
                    {
                        return thiefGoosePrefab;
                    }
                    Debug.LogWarning($"Thief Goose prefab not assigned, using basic prefab for {enemyName}");
                    break;
                    
                case "面具疯子":
                    if (maskedManiacPrefab != null)
                    {
                        return maskedManiacPrefab;
                    }
                    Debug.LogWarning($"Masked Maniac prefab not assigned, using basic prefab for {enemyName}");
                    break;
                    
                default:
                    Debug.Log($"No specific prefab found for {enemyName}, using basic prefab");
                    break;
            }
            
            // 回退到基础预制体
            if (basicEnemyPrefab != null)
            {
                return basicEnemyPrefab;
            }
            
            Debug.LogError("No enemy prefab assigned! Please assign at least the basic enemy prefab.");
            return null;
        }
        
        /// <summary>
        /// 根据敌人名称获取对应的显示预制体
        /// </summary>
        private GameObject GetDisplayPrefab(string enemyName)
        {
            switch (enemyName)
            {
                case "小偷大鹅":
                    if (thiefGooseDisplayPrefab != null)
                    {
                        return thiefGooseDisplayPrefab;
                    }
                    Debug.LogWarning($"Thief Goose display prefab not assigned, using basic display prefab for {enemyName}");
                    break;
                    
                case "面具疯子":
                    if (maskedManiacDisplayPrefab != null)
                    {
                        return maskedManiacDisplayPrefab;
                    }
                    Debug.LogWarning($"Masked Maniac display prefab not assigned, using basic display prefab for {enemyName}");
                    break;
                    
                default:
                    Debug.Log($"No specific display prefab found for {enemyName}, using basic display prefab");
                    break;
            }
            
            // 回退到基础显示预制体
            if (basicEnemyDisplayPrefab != null)
            {
                return basicEnemyDisplayPrefab;
            }
            
            Debug.LogError("No display prefab assigned! Please assign at least the basic enemy display prefab.");
            return null;
        }
        
        /// <summary>
        /// 初始化显示预制体
        /// </summary>
        public void InitializeDisplayPrefabs()
        {
            // 清理现有的显示预制体
            ClearDisplayPrefabs();
            
            // 确保数组长度匹配
            int count = Mathf.Min(displayPositions.Length, displayEnemyTypes.Length);
            
            for (int i = 0; i < count; i++)
            {
                string enemyType = displayEnemyTypes[i];
                Vector3 position = displayPositions[i];
                Vector3 scale = i < displayScales.Length ? displayScales[i] : Vector3.one;
                
                // 获取对应的显示预制体
                GameObject displayPrefab = GetDisplayPrefab(enemyType);
                if (displayPrefab != null)
                {
                    // 实例化显示预制体
                    GameObject displayInstance = Instantiate(displayPrefab, position, Quaternion.identity);
                    displayInstance.name = $"Display_{enemyType}_{i}";
                    
                    // 设置大小
                    displayInstance.transform.localScale = scale;
                    
                    // 可选：禁用战斗相关组件，只保留显示功能
                    DisableCombatComponents(displayInstance);
                    
                    // 添加动画组件
                    AddAnimationComponent(displayInstance);
                    
                    displayPrefabs.Add(displayInstance);
                    Debug.Log($"Initialized display prefab: {displayInstance.name} at position {position} with scale {scale}");
                }
                else
                {
                    Debug.LogError($"Failed to get display prefab for enemy type: {enemyType}");
                }
            }
            
            Debug.Log($"Initialized {displayPrefabs.Count} display prefabs");
        }
        
        /// <summary>
        /// 为战斗敌人分配对应的显示预制体
        /// </summary>
        public void AssignDisplayPrefabToEnemy(CombatEnemyInterface enemy, GameObject displayPrefab)
        {
            if (enemy != null && displayPrefab != null)
            {
                enemyToDisplayPrefabMap[enemy] = displayPrefab;
                Debug.Log($"Assigned display prefab {displayPrefab.name} to enemy {enemy.name}");
            }
        }
        
        /// <summary>
        /// 根据战斗敌人清除对应的显示预制体
        /// </summary>
        public void ClearDisplayPrefabByEnemy(CombatEnemyInterface enemy)
        {
            if (enemyToDisplayPrefabMap.ContainsKey(enemy))
            {
                GameObject displayPrefab = enemyToDisplayPrefabMap[enemy];
                if (displayPrefab != null)
                {
                    displayPrefabs.Remove(displayPrefab);
                    DestroyImmediate(displayPrefab);
                    Debug.Log($"Cleared display prefab {displayPrefab.name} for enemy {enemy.name}");
                }
                enemyToDisplayPrefabMap.Remove(enemy);
            }
            else
            {
                Debug.LogWarning($"No display prefab found for enemy: {enemy.name}");
            }
        }
        
        /// <summary>
        /// 根据敌人名称清除对应的显示预制体
        /// </summary>
        public void ClearDisplayPrefabByEnemyName(string enemyName)
        {
            List<GameObject> prefabsToRemove = new List<GameObject>();
            
            foreach (GameObject prefab in displayPrefabs)
            {
                if (prefab != null && prefab.name.Contains(enemyName))
                {
                    prefabsToRemove.Add(prefab);
                    Debug.Log($"Found display prefab to remove: {prefab.name} for enemy: {enemyName}");
                }
            }
            
            // 移除找到的预制体
            foreach (GameObject prefab in prefabsToRemove)
            {
                displayPrefabs.Remove(prefab);
                DestroyImmediate(prefab);
            }
            
            if (prefabsToRemove.Count > 0)
            {
                Debug.Log($"Cleared {prefabsToRemove.Count} display prefab(s) for enemy: {enemyName}");
            }
            else
            {
                Debug.LogWarning($"No display prefab found for enemy: {enemyName}");
            }
        }
        
        /// <summary>
        /// 清理显示预制体
        /// </summary>
        public void ClearDisplayPrefabs()
        {
            foreach (GameObject prefab in displayPrefabs)
            {
                if (prefab != null)
                {
                    DestroyImmediate(prefab);
                }
            }
            displayPrefabs.Clear();
            Debug.Log("Cleared all display prefabs");
        }
        
        /// <summary>
        /// 清理所有敌人相关的预制体（包括显示预制体）
        /// </summary>
        public void ClearAllEnemyPrefabs()
        {
            // 清理显示预制体
            ClearDisplayPrefabs();
            
            // 清理战斗敌人（通过CombatManager）
            CombatManager combatManager = FindObjectOfType<CombatManager>();
            if (combatManager != null && combatManager._combatEnemies != null)
            {
                foreach (CombatEnemyInterface enemy in combatManager._combatEnemies.ToArray())
                {
                    if (enemy != null)
                    {
                        combatManager.RemoveEnemy(enemy);
                    }
                }
            }
            
            Debug.Log("Cleared all enemy prefabs (display and combat)");
        }
        
        /// <summary>
        /// 在场景卸载时清理显示预制体
        /// </summary>
        void OnDestroy()
        {
            ClearDisplayPrefabs();
        }
        
        /// <summary>
        /// 在应用退出时清理显示预制体
        /// </summary>
        void OnApplicationQuit()
        {
            ClearDisplayPrefabs();
        }
        
        /// <summary>
        /// 禁用战斗相关组件，只保留显示功能
        /// </summary>
        private void DisableCombatComponents(GameObject displayPrefab)
        {
            // 禁用CombatEnemyInterface组件
            CombatEnemyInterface enemyInterface = displayPrefab.GetComponent<CombatEnemyInterface>();
            if (enemyInterface != null)
            {
                enemyInterface.enabled = false;
            }
            
            // 禁用EnemySkillManager组件
            EnemySkillManager skillManager = displayPrefab.GetComponent<EnemySkillManager>();
            if (skillManager != null)
            {
                skillManager.enabled = false;
            }
            
            // 禁用CombatEnemyUI组件（如果不需要UI显示）
            CombatEnemyUI enemyUI = displayPrefab.GetComponent<CombatEnemyUI>();
            if (enemyUI != null)
            {
                enemyUI.enabled = false;
            }
        }
        
        /// <summary>
        /// 添加动画组件到显示预制体
        /// </summary>
        private void AddAnimationComponent(GameObject displayPrefab)
        {
            // 检查是否已经有DisplayPrefabAnimator组件
            DisplayPrefabAnimator animator = displayPrefab.GetComponent<DisplayPrefabAnimator>();
            if (animator == null)
            {
                // 添加动画组件
                animator = displayPrefab.AddComponent<DisplayPrefabAnimator>();
                Debug.Log($"Added DisplayPrefabAnimator to {displayPrefab.name}");
            }
            else
            {
                Debug.Log($"DisplayPrefabAnimator already exists on {displayPrefab.name}");
            }
        }
        
        /// <summary>
        /// 更新显示预制体的位置
        /// </summary>
        public void UpdateDisplayPositions(Vector3[] newPositions)
        {
            if (newPositions.Length != displayPositions.Length)
            {
                Debug.LogWarning($"New positions array length ({newPositions.Length}) doesn't match current length ({displayPositions.Length})");
                return;
            }
            
            displayPositions = newPositions;
            
            // 更新现有预制体的位置
            for (int i = 0; i < displayPrefabs.Count && i < displayPositions.Length; i++)
            {
                if (displayPrefabs[i] != null)
                {
                    displayPrefabs[i].transform.position = displayPositions[i];
                }
            }
            
            Debug.Log("Updated display prefab positions");
        }
        
        /// <summary>
        /// 更新显示预制体的敌人类型
        /// </summary>
        public void UpdateDisplayEnemyTypes(string[] newEnemyTypes)
        {
            if (newEnemyTypes.Length != displayEnemyTypes.Length)
            {
                Debug.LogWarning($"New enemy types array length ({newEnemyTypes.Length}) doesn't match current length ({displayEnemyTypes.Length})");
                return;
            }
            
            displayEnemyTypes = newEnemyTypes;
            
            // 重新初始化显示预制体
            InitializeDisplayPrefabs();
        }
        
        /// <summary>
        /// 更新显示预制体的大小
        /// </summary>
        public void UpdateDisplayScales(Vector3[] newScales)
        {
            if (newScales.Length != displayScales.Length)
            {
                Debug.LogWarning($"New scales array length ({newScales.Length}) doesn't match current length ({displayScales.Length})");
                return;
            }
            
            displayScales = newScales;
            
            // 更新现有预制体的大小
            for (int i = 0; i < displayPrefabs.Count && i < displayScales.Length; i++)
            {
                if (displayPrefabs[i] != null)
                {
                    displayPrefabs[i].transform.localScale = displayScales[i];
                }
            }
            
            Debug.Log("Updated display prefab scales");
        }
        
        /// <summary>
        /// 根据名称生成敌人
        /// </summary>
        public CombatEnemyInterface GenerateEnemyByName(string enemyName, Vector3 position)
        {
            if (enemyDatabase == null)
            {
                Debug.LogError("EnemyDatabase is not assigned!");
                return null;
            }
            
            EnemyData enemyData = enemyDatabase.GetEnemyByName(enemyName);
            if (enemyData == null)
            {
                return null;
            }
            
            return GenerateEnemy(enemyData, position);
        }
        
        /// <summary>
        /// 根据难度随机生成敌人
        /// </summary>
        public CombatEnemyInterface GenerateRandomEnemyByDifficulty(int difficulty, Vector3 position)
        {
            if (enemyDatabase == null)
            {
                Debug.LogError("EnemyDatabase is not assigned!");
                return null;
            }
            
            List<EnemyData> availableEnemies = enemyDatabase.GetEnemiesByDifficulty(difficulty);
            if (availableEnemies.Count == 0)
            {
                Debug.LogWarning($"No enemies found with difficulty {difficulty}");
                return null;
            }
            
            EnemyData selectedEnemy = availableEnemies[Random.Range(0, availableEnemies.Count)];
            return GenerateEnemy(selectedEnemy, position);
        }
        
        /// <summary>
        /// 生成敌人的核心方法
        /// </summary>
        private CombatEnemyInterface GenerateEnemy(EnemyData enemyData, Vector3 position)
        {
            // 根据敌人名称选择对应的预制体
            GameObject enemyPrefab = GetEnemyPrefab(enemyData.enemyName);
            if (enemyPrefab == null)
            {
                Debug.LogError($"Failed to get prefab for enemy: {enemyData.enemyName}");
                return null;
            }
            
            // 实例化敌人对象
            GameObject enemyObject = Instantiate(enemyPrefab, position, Quaternion.identity);
            enemyObject.name = enemyData.enemyName;
            
            // 获取CombatEnemyInterface组件
            CombatEnemyInterface enemyInterface = enemyObject.GetComponent<CombatEnemyInterface>();
            if (enemyInterface == null)
            {
                Debug.LogError($"Enemy prefab for {enemyData.enemyName} must have CombatEnemyInterface component!");
                Destroy(enemyObject);
                return null;
            }
            
            // 设置敌人属性
            enemyInterface._maxHP = enemyData.maxHP;
            enemyInterface._currentHP = enemyData.currentHP;
            
            // 添加EnemySkillManager组件来管理技能
            EnemySkillManager skillManager = enemyObject.GetComponent<EnemySkillManager>();
            if (skillManager == null)
            {
                skillManager = enemyObject.AddComponent<EnemySkillManager>();
            }
            
            // 初始化技能系统
            skillManager.InitializeSkills(enemyData);
            
            // 设置初始的actionsLeft为当前技能的actionCount
            if (skillManager.currentSkill != null)
            {
                enemyInterface._actionsLeft = skillManager.currentActionCount;
            }
            else
            {
                enemyInterface._actionsLeft = 3; // 默认值
            }
            
            // 清空原有的ActionBank，使用新的技能系统
            enemyInterface._actionBank.Clear();
            
            // 为这个敌人分配对应的显示预制体（如果存在）
            AssignDisplayPrefabToEnemyIfAvailable(enemyInterface, enemyData.enemyName);
            
            Debug.Log($"Generated enemy: {enemyData.enemyName} using prefab: {enemyPrefab.name}");
            return enemyInterface;
        }
        
        /// <summary>
        /// 为敌人分配对应的显示预制体（如果可用）
        /// </summary>
        private void AssignDisplayPrefabToEnemyIfAvailable(CombatEnemyInterface enemy, string enemyType)
        {
            // 查找对应的显示预制体
            foreach (GameObject displayPrefab in displayPrefabs)
            {
                if (displayPrefab != null && displayPrefab.name.Contains(enemyType))
                {
                    // 检查这个显示预制体是否已经被分配
                    bool alreadyAssigned = false;
                    foreach (var kvp in enemyToDisplayPrefabMap)
                    {
                        if (kvp.Value == displayPrefab)
                        {
                            alreadyAssigned = true;
                            break;
                        }
                    }
                    
                    if (!alreadyAssigned)
                    {
                        AssignDisplayPrefabToEnemy(enemy, displayPrefab);
                        return;
                    }
                }
            }
            
            Debug.LogWarning($"No available display prefab found for enemy: {enemy.name}");
        }
        
        /// <summary>
        /// 测试生成小偷大鹅
        /// </summary>
        [ContextMenu("Test Generate Thief Goose")]
        public void TestGenerateThiefGoose()
        {
            GenerateEnemyByName("小偷大鹅", Vector3.zero);
        }

        /// <summary>
        /// 测试生成面具疯子
        /// </summary>
        [ContextMenu("Test Generate Masked Maniac")]
        public void TestGenerateMaskedManiac()
        {
            GenerateEnemyByName("面具疯子", Vector3.zero);
        }
        
        /// <summary>
        /// 测试生成多个不同敌人（验证预制体）
        /// </summary>
        [ContextMenu("Test Generate Multiple Enemies")]
        public void TestGenerateMultipleEnemies()
        {
            // 生成小偷大鹅
            CombatEnemyInterface thiefGoose = GenerateEnemyByName("小偷大鹅", new Vector3(-2, 0, 0));
            
            // 生成面具疯子
            CombatEnemyInterface maskedManiac = GenerateEnemyByName("面具疯子", new Vector3(2, 0, 0));
            
            Debug.Log("Generated multiple enemies with different prefabs for testing");
        }
        
        /// <summary>
        /// 测试初始化显示预制体
        /// </summary>
        [ContextMenu("Test Initialize Display Prefabs")]
        public void TestInitializeDisplayPrefabs()
        {
            InitializeDisplayPrefabs();
        }
        
        /// <summary>
        /// 测试清理显示预制体
        /// </summary>
        [ContextMenu("Test Clear Display Prefabs")]
        public void TestClearDisplayPrefabs()
        {
            ClearDisplayPrefabs();
        }
        
        /// <summary>
        /// 测试根据敌人名称清除显示预制体
        /// </summary>
        [ContextMenu("Test Clear Display Prefab by Enemy Name")]
        public void TestClearDisplayPrefabByEnemyName()
        {
            ClearDisplayPrefabByEnemyName("小偷大鹅");
        }
        
        /// <summary>
        /// 测试一对一对应关系
        /// </summary>
        [ContextMenu("Test One-to-One Mapping")]
        public void TestOneToOneMapping()
        {
            Debug.Log("=== One-to-One Mapping Test ===");
            Debug.Log($"Total display prefabs: {displayPrefabs.Count}");
            Debug.Log($"Total enemy mappings: {enemyToDisplayPrefabMap.Count}");
            
            foreach (var kvp in enemyToDisplayPrefabMap)
            {
                Debug.Log($"Enemy: {kvp.Key.name} -> Display: {kvp.Value.name}");
            }
        }
        
        /// <summary>
        /// 测试清理所有敌人预制体
        /// </summary>
        [ContextMenu("Test Clear All Enemy Prefabs")]
        public void TestClearAllEnemyPrefabs()
        {
            ClearAllEnemyPrefabs();
        }
        
        /// <summary>
        /// 测试更新显示预制体大小
        /// </summary>
        [ContextMenu("Test Update Display Scales")]
        public void TestUpdateDisplayScales()
        {
            Vector3[] testScales = new Vector3[3] 
            {
                new Vector3(1.5f, 1.5f, 1.5f),
                new Vector3(0.8f, 0.8f, 0.8f),
                new Vector3(1.2f, 1.2f, 1.2f)
            };
            UpdateDisplayScales(testScales);
        }
        
        /// <summary>
        /// 验证预制体设置
        /// </summary>
        [ContextMenu("Validate Prefab Setup")]
        public void ValidatePrefabSetup()
        {
            Debug.Log("=== Enemy Prefab Validation ===");
            
            if (thiefGoosePrefab != null)
            {
                Debug.Log($"✓ Thief Goose prefab: {thiefGoosePrefab.name}");
                if (thiefGoosePrefab.GetComponent<CombatEnemyInterface>() != null)
                {
                    Debug.Log("  ✓ Has CombatEnemyInterface component");
                }
                else
                {
                    Debug.LogError("  ✗ Missing CombatEnemyInterface component");
                }
            }
            else
            {
                Debug.LogWarning("✗ Thief Goose prefab not assigned");
            }
            
            if (maskedManiacPrefab != null)
            {
                Debug.Log($"✓ Masked Maniac prefab: {maskedManiacPrefab.name}");
                if (maskedManiacPrefab.GetComponent<CombatEnemyInterface>() != null)
                {
                    Debug.Log("  ✓ Has CombatEnemyInterface component");
                }
                else
                {
                    Debug.LogError("  ✗ Missing CombatEnemyInterface component");
                }
            }
            else
            {
                Debug.LogWarning("✗ Masked Maniac prefab not assigned");
            }
            
            if (basicEnemyPrefab != null)
            {
                Debug.Log($"✓ Basic enemy prefab: {basicEnemyPrefab.name}");
                if (basicEnemyPrefab.GetComponent<CombatEnemyInterface>() != null)
                {
                    Debug.Log("  ✓ Has CombatEnemyInterface component");
                }
                else
                {
                    Debug.LogError("  ✗ Missing CombatEnemyInterface component");
                }
            }
            else
            {
                Debug.LogError("✗ Basic enemy prefab not assigned");
            }
            
            Debug.Log("=== Display Prefab Validation ===");
            
            if (thiefGooseDisplayPrefab != null)
            {
                Debug.Log($"✓ Thief Goose display prefab: {thiefGooseDisplayPrefab.name}");
            }
            else
            {
                Debug.LogWarning("✗ Thief Goose display prefab not assigned");
            }
            
            if (maskedManiacDisplayPrefab != null)
            {
                Debug.Log($"✓ Masked Maniac display prefab: {maskedManiacDisplayPrefab.name}");
            }
            else
            {
                Debug.LogWarning("✗ Masked Maniac display prefab not assigned");
            }
            
            if (basicEnemyDisplayPrefab != null)
            {
                Debug.Log($"✓ Basic enemy display prefab: {basicEnemyDisplayPrefab.name}");
            }
            else
            {
                Debug.LogWarning("✗ Basic enemy display prefab not assigned");
            }
            
            Debug.Log("=== Display Settings Validation ===");
            
            Debug.Log($"Display Positions: {displayPositions.Length} positions configured");
            Debug.Log($"Display Enemy Types: {displayEnemyTypes.Length} types configured");
            Debug.Log($"Display Scales: {displayScales.Length} scales configured");
            
            for (int i = 0; i < Mathf.Min(displayPositions.Length, displayEnemyTypes.Length, displayScales.Length); i++)
            {
                Debug.Log($"  Position {i}: {displayEnemyTypes[i]} at {displayPositions[i]} with scale {displayScales[i]}");
            }
            
            Debug.Log("=== Validation Complete ===");
        }
    }
}
