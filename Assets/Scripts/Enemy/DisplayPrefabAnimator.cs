using UnityEngine;
using MoreMountains.Feedbacks;

namespace EOP.Enemy
{
    /// <summary>
    /// 显示预制体动画管理器
    /// 使用Feel插件的MMWiggle组件为显示预制体添加漂浮等动画效果
    /// </summary>
    public class DisplayPrefabAnimator : MonoBehaviour
    {
        [Header("漂浮动画设置")]
        [Tooltip("是否启用漂浮动画")]
        public bool enableFloating = true;
        
        [Tooltip("漂浮幅度")]
        public float floatingAmplitude = 0.5f;
        
        [Tooltip("漂浮频率")]
        public float floatingFrequency = 1f;
        
        [Tooltip("漂浮方向")]
        public Vector3 floatingDirection = Vector3.up;
        
        [Header("旋转动画设置")]
        [Tooltip("是否启用旋转动画")]
        public bool enableRotation = false;
        
        [Tooltip("旋转速度（度/秒）")]
        public float rotationSpeed = 30f;
        
        [Tooltip("旋转轴")]
        public Vector3 rotationAxis = Vector3.up;
        
        [Header("缩放动画设置")]
        [Tooltip("是否启用缩放动画")]
        public bool enableScaling = false;
        
        [Toolt<PERSON>("缩放幅度")]
        public float scalingAmplitude = 0.1f;
        
        [Tooltip("缩放频率")]
        public float scalingFrequency = 2f;
        
        private MMWiggle wiggleComponent;
        private Vector3 originalPosition;
        private Vector3 originalScale;
        
        void Awake()
        {
            // 获取或添加MMWiggle组件
            wiggleComponent = GetComponent<MMWiggle>();
            if (wiggleComponent == null)
            {
                wiggleComponent = gameObject.AddComponent<MMWiggle>();
            }
            
            // 记录原始位置和大小
            originalPosition = transform.position;
            originalScale = transform.localScale;
        }
        
        void Start()
        {
            SetupAnimations();
        }
        
        /// <summary>
        /// 设置动画效果
        /// </summary>
        public void SetupAnimations()
        {
            if (wiggleComponent == null) return;
            
            // 设置位置漂浮动画
            if (enableFloating)
            {
                SetupFloatingAnimation();
            }
            
            // 设置旋转动画
            if (enableRotation)
            {
                SetupRotationAnimation();
            }
            
            // 设置缩放动画
            if (enableScaling)
            {
                SetupScalingAnimation();
            }
        }
        
        /// <summary>
        /// 设置漂浮动画
        /// </summary>
        private void SetupFloatingAnimation()
        {
            // 配置MMWiggle的位置动画
            wiggleComponent.PositionActive = true;
            wiggleComponent.PositionWiggleProperties.AmplitudeMin = -floatingDirection * floatingAmplitude;
            wiggleComponent.PositionWiggleProperties.AmplitudeMax = floatingDirection * floatingAmplitude;
            wiggleComponent.PositionWiggleProperties.FrequencyMin = floatingFrequency;
            wiggleComponent.PositionWiggleProperties.FrequencyMax = floatingFrequency;
            wiggleComponent.PositionWiggleProperties.NoiseFrequencyMin = Vector3.one * floatingFrequency;
            wiggleComponent.PositionWiggleProperties.NoiseFrequencyMax = Vector3.one * floatingFrequency;
            wiggleComponent.PositionWiggleProperties.UseUnscaledTime = true;
            
            // 启动位置动画（持续时间为0表示无限循环）
            wiggleComponent.WigglePosition(0f);
        }
        
        /// <summary>
        /// 设置旋转动画
        /// </summary>
        private void SetupRotationAnimation()
        {
            // 配置MMWiggle的旋转动画
            wiggleComponent.RotationActive = true;
            wiggleComponent.RotationWiggleProperties.AmplitudeMin = -rotationAxis * rotationSpeed;
            wiggleComponent.RotationWiggleProperties.AmplitudeMax = rotationAxis * rotationSpeed;
            wiggleComponent.RotationWiggleProperties.FrequencyMin = 1f;
            wiggleComponent.RotationWiggleProperties.FrequencyMax = 1f;
            wiggleComponent.RotationWiggleProperties.UseUnscaledTime = true;
            
            // 启动旋转动画（持续时间为0表示无限循环）
            wiggleComponent.WiggleRotation(0f);
        }
        
        /// <summary>
        /// 设置缩放动画
        /// </summary>
        private void SetupScalingAnimation()
        {
            // 配置MMWiggle的缩放动画
            wiggleComponent.ScaleActive = true;
            wiggleComponent.ScaleWiggleProperties.AmplitudeMin = Vector3.one * (1f - scalingAmplitude);
            wiggleComponent.ScaleWiggleProperties.AmplitudeMax = Vector3.one * (1f + scalingAmplitude);
            wiggleComponent.ScaleWiggleProperties.FrequencyMin = scalingFrequency;
            wiggleComponent.ScaleWiggleProperties.FrequencyMax = scalingFrequency;
            wiggleComponent.ScaleWiggleProperties.UseUnscaledTime = true;
            
            // 启动缩放动画（持续时间为0表示无限循环）
            wiggleComponent.WiggleScale(0f);
        }
        
        /// <summary>
        /// 停止所有动画
        /// </summary>
        public void StopAllAnimations()
        {
            if (wiggleComponent != null)
            {
                // 停止所有动画
                wiggleComponent.PositionActive = false;
                wiggleComponent.RotationActive = false;
                wiggleComponent.ScaleActive = false;
            }
        }
        
        /// <summary>
        /// 恢复原始状态
        /// </summary>
        public void ResetToOriginal()
        {
            StopAllAnimations();
            transform.position = originalPosition;
            transform.localScale = originalScale;
        }
        
        /// <summary>
        /// 设置漂浮参数
        /// </summary>
        public void SetFloatingParameters(float amplitude, float frequency, Vector3 direction)
        {
            floatingAmplitude = amplitude;
            floatingFrequency = frequency;
            floatingDirection = direction;
            
            if (enableFloating)
            {
                SetupFloatingAnimation();
            }
        }
        
        /// <summary>
        /// 设置旋转参数
        /// </summary>
        public void SetRotationParameters(float speed, Vector3 axis)
        {
            rotationSpeed = speed;
            rotationAxis = axis;
            
            if (enableRotation)
            {
                SetupRotationAnimation();
            }
        }
        
        /// <summary>
        /// 设置缩放参数
        /// </summary>
        public void SetScalingParameters(float amplitude, float frequency)
        {
            scalingAmplitude = amplitude;
            scalingFrequency = frequency;
            
            if (enableScaling)
            {
                SetupScalingAnimation();
            }
        }
        
        /// <summary>
        /// 测试漂浮动画
        /// </summary>
        [ContextMenu("Test Floating Animation")]
        public void TestFloatingAnimation()
        {
            enableFloating = true;
            enableRotation = false;
            enableScaling = false;
            SetupAnimations();
        }
        
        /// <summary>
        /// 测试旋转动画
        /// </summary>
        [ContextMenu("Test Rotation Animation")]
        public void TestRotationAnimation()
        {
            enableFloating = false;
            enableRotation = true;
            enableScaling = false;
            SetupAnimations();
        }
        
        /// <summary>
        /// 测试缩放动画
        /// </summary>
        [ContextMenu("Test Scaling Animation")]
        public void TestScalingAnimation()
        {
            enableFloating = false;
            enableRotation = false;
            enableScaling = true;
            SetupAnimations();
        }
        
        /// <summary>
        /// 测试组合动画
        /// </summary>
        [ContextMenu("Test Combined Animation")]
        public void TestCombinedAnimation()
        {
            enableFloating = true;
            enableRotation = true;
            enableScaling = true;
            SetupAnimations();
        }
        
        /// <summary>
        /// 停止动画
        /// </summary>
        [ContextMenu("Stop All Animations")]
        public void StopAnimations()
        {
            StopAllAnimations();
        }
    }
} 