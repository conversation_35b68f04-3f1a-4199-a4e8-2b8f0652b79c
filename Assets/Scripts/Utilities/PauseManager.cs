using UnityEngine;
public struct PauseEvent{}
public struct ResumeEvent{}
public class PauseManager : MonoBehaviour
{
    public static PauseManager Instance;

    [<PERSON>er("Pause Menu UI")]
    public GameObject pauseMenuUI;

    [<PERSON>er("Settings")]
    public KeyCode toggleKey = KeyCode.Escape;

    public bool IsPaused { get; private set; } = false;

    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Update()
    {
        if (Input.GetKeyDown(toggleKey))
        {
            TogglePause();
        }
        
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }

    public void TogglePause()
    {
        if (IsPaused)
            ResumeGame();
        else
            PauseGame();
    }

    public void PauseGame()
    {
        EventBetter.Raise(new PauseEvent());
        IsPaused = true;
        if (pauseMenuUI != null) pauseMenuUI.SetActive(true);
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }

    public void ResumeGame()
    {
        EventBetter.Raise(new ResumeEvent());
        IsPaused = false;
        if (pauseMenuUI != null) pauseMenuUI.SetActive(false);
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
}