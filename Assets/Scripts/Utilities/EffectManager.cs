using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using EOP.Utilites;
using Sirenix.OdinInspector;
using System.Linq;

public class PriorityQueue<T>
{
    private readonly List<(T Item, float Priority)> _items = new();

    public int Count => _items.Count;

    public void Enqueue(T item, float priority)
    {
        _items.Add((item, priority));
        _items.Sort((a, b) => b.Priority.CompareTo(a.Priority)); // Higher priority first
    }

    public T Dequeue()
    {
        if (_items.Count == 0) throw new InvalidOperationException("Queue is empty");
        var item = _items[0].Item;
        _items.RemoveAt(0);
        return item;
    }

    public void Clear() => _items.Clear();
}

[Serializable]
public struct ShakeSizeConfig
{
    public string name;
    public float intensity;
    public float duration;
}

[Serializable]
public struct BloomSizeConfig
{
    public string name;
    public float intensity;
    public float duration;
}

[Serializable]
public struct ZoomRequest
{
    public float zoomLevel;
    public float duration;
    public Vector3 worldCenter;

    public ZoomRequest(float zoomLevel, float duration, Vector3 worldCenter)
    {
        this.zoomLevel = zoomLevel;
        this.duration = duration;
        this.worldCenter = worldCenter;
    }

    public ZoomRequest(string name, Vector3 worldCenter)
    {
        var result = EffectManager.Instance.zoomSizes.ToList().Find(x => x.name == name);
        if (result.name == null)
        {
            throw new ArgumentException("Invalid zoom size");
        }
        else
        {
            zoomLevel = result.zoomLevel;
            duration = result.duration;
            this.worldCenter = worldCenter;
        }
    }
}

[Serializable]
public struct ResetZoomRequest
{
    public float resetDuration;

    public ResetZoomRequest(float resetDuration)
    {
        this.resetDuration = resetDuration;
    }
}

[Serializable]
public struct ZoomSizeConfig
{
    public string name;
    public float zoomLevel;
    public float duration;
}

public class EffectManager : Singleton<EffectManager>
{
    // Shake queue
    private readonly PriorityQueue<CamShakeRequest> _shakeQueue = new();
    private bool _isShaking = false;
    private CancellationTokenSource _shakeCTS;

    [Header("Shake Sizes")] public ShakeSizeConfig[] shakeSizes = new ShakeSizeConfig[]
    {
        new ShakeSizeConfig { name = "Small", intensity = 0.1f, duration = 0.1f },
        new ShakeSizeConfig { name = "Medium", intensity = 0.2f, duration = 0.2f },
        new ShakeSizeConfig { name = "Strong", intensity = 0.5f, duration = 0.2f },
    };

    [Header("Camera Shake Settings")] public Transform cameraTransform;
    public float shakeFrequency = 30f;

    // Bloom queue
    private readonly PriorityQueue<BloomRequest> _bloomQueue = new();
    private bool _isBlooming = false;
    private CancellationTokenSource _bloomCTS;

    [Header("Bloom Sizes")] public BloomSizeConfig[] bloomSizes = new BloomSizeConfig[]
    {
        new BloomSizeConfig { name = "Small", intensity = 0.5f, duration = 0.1f },
        new BloomSizeConfig { name = "Medium", intensity = 0.7f, duration = 0.2f },
        new BloomSizeConfig { name = "Strong", intensity = 0.9f, duration = 0.2f },
    };

    // Zoom queue
    private readonly PriorityQueue<ZoomRequest> _zoomQueue = new();
    private readonly PriorityQueue<ResetZoomRequest> _resetZoomQueue = new();
    private bool _isZooming = false;
    private CancellationTokenSource _zoomCTS;

    [Header("Zoom Sizes")] public ZoomSizeConfig[] zoomSizes = new ZoomSizeConfig[]
    {
        new ZoomSizeConfig { name = "Small", zoomLevel = 1.2f, duration = 0.5f },
        new ZoomSizeConfig { name = "Medium", zoomLevel = 1.5f, duration = 1f },
        new ZoomSizeConfig { name = "Strong", zoomLevel = 2f, duration = 1.5f },
    };

    public Volume volume;

    private Bloom _bloom;
    private LensDistortion _lensDistortion;
    private float _originalLensDistortionIntensity;
    private Vector2 _originalLensDistortionCenter;

    protected override void Awake()
    {
        base.Awake();

        if (!volume.profile.TryGet(out _bloom))
        {
            Debug.LogWarning("Bloom effect not found.");
        }

        if (!volume.profile.TryGet(out _lensDistortion))
        {
            Debug.LogWarning("Lens Distortion effect not found.");
        }

        // Store original values
        _originalLensDistortionIntensity = _lensDistortion?.intensity.value ?? 0f;
        _originalLensDistortionCenter = _lensDistortion?.center.value ?? Vector2.zero;

        // Listen for events
        EventBetter.Listen(this, (CamShakeRequest sr) => RequestShake(sr));
        EventBetter.Listen(this, (BloomRequest br) => RequestBloom(br));
        EventBetter.Listen(this, (ZoomRequest zr) => RequestZoom(zr));
    }

    private void OnDisable()
    {
        _shakeCTS?.Cancel();
        _bloomCTS?.Cancel();
        _zoomCTS?.Cancel();
    }

    #region Camera Shake

    public void RequestShake(CamShakeRequest request)
    {
        _shakeQueue.Enqueue(request, request.intensity); // higher intensity = higher priority

        if (!_isShaking)
        {
            _ = ProcessShakeQueueAsync();
        }
    }

    private async Task ProcessShakeQueueAsync()
    {
        _isShaking = true;

        while (_shakeQueue.Count > 0)
        {
            var request = _shakeQueue.Dequeue();

            _shakeCTS?.Cancel();
            _shakeCTS = new CancellationTokenSource();

            try
            {
                await ShakeAsync(request.intensity, request.duration, _shakeCTS.Token);
            }
            catch (TaskCanceledException)
            {
            }
        }

        _isShaking = false;
    }

    private async Task ShakeAsync(float intensity, float duration, CancellationToken token)
    {
        if (cameraTransform == null)
        {
            Debug.LogWarning("Camera Transform not assigned.");
            return;
        }

        Vector3 originalPos = cameraTransform.localPosition;
        float elapsed = 0f;
        float interval = 1f / shakeFrequency;

        while (elapsed < duration)
        {
            token.ThrowIfCancellationRequested();

            cameraTransform.localPosition = originalPos + (Vector3)UnityEngine.Random.insideUnitCircle * intensity;

            await Task.Delay((int)(interval * 1000f), token);
            elapsed += interval;
        }

        cameraTransform.localPosition = originalPos;
    }

    #endregion

    #region Bloom Control

    public void RequestBloom(BloomRequest request)
    {
        _bloomQueue.Enqueue(request, request.intensity); // priority = intensity

        if (!_isBlooming)
        {
            _ = ProcessBloomQueueAsync();
        }
    }

    private async Task ProcessBloomQueueAsync()
    {
        _isBlooming = true;

        while (_bloomQueue.Count > 0)
        {
            var request = _bloomQueue.Dequeue();

            _bloomCTS?.Cancel();
            _bloomCTS = new CancellationTokenSource();

            try
            {
                await BloomEffectAsync(request.intensity, request.duration, _bloomCTS.Token);
            }
            catch (TaskCanceledException)
            {
            }
        }

        _isBlooming = false;
    }

    private async Task BloomEffectAsync(float intensity, float duration, CancellationToken token)
    {
        if (_bloom == null)
        {
            Debug.LogWarning("Bloom effect not found.");
            return;
        }

        float originalIntensity = _bloom.intensity.value;
        _bloom.intensity.value = intensity;

        float elapsed = 0f;
        while (elapsed < duration)
        {
            token.ThrowIfCancellationRequested();
            await Task.Yield();
            elapsed += Time.unscaledDeltaTime;
        }

        _bloom.intensity.value = originalIntensity;
    }

    #endregion

    #region Camera Zoom

    public void RequestZoom(ZoomRequest request)
    {
        _zoomQueue.Enqueue(request, request.zoomLevel); // higher zoomLevel = higher priority

        if (!_isZooming)
        {
            _ = ProcessZoomQueueAsync();
        }
    }

    private async Task ProcessZoomQueueAsync()
    {
        _isZooming = true;

        while (_zoomQueue.Count > 0 || _resetZoomQueue.Count > 0)
        {
            if (_zoomQueue.Count > 0)
            {
                var request = _zoomQueue.Dequeue();

                _zoomCTS?.Cancel();
                _zoomCTS = new CancellationTokenSource();

                try
                {
                    await ZoomEffectAsync(request.zoomLevel, request.duration, request.worldCenter, _zoomCTS.Token);
                }
                catch (TaskCanceledException)
                {
                }
            }

            if (_resetZoomQueue.Count > 0)
            {
                var resetRequest = _resetZoomQueue.Dequeue();

                _zoomCTS?.Cancel();
                _zoomCTS = new CancellationTokenSource();

                try
                {
                    await ZoomResetAsync(resetRequest.resetDuration, _zoomCTS.Token);
                }
                catch (TaskCanceledException)
                {
                }
            }
        }

        _isZooming = false;
    }

    private async Task ZoomEffectAsync(float zoomLevel, float duration, Vector3 worldCenter, CancellationToken token)
    {
        if (_lensDistortion == null)
        {
            Debug.LogWarning("Lens Distortion effect not found.");
            return;
        }

        if (cameraTransform == null)
        {
            Debug.LogWarning("Camera Transform not assigned.");
            return;
        }

        // Store original values
        float originalZoom = _lensDistortion.intensity.value;
        Vector2 originalCenter = _lensDistortion.center.value;

        // Convert world center to screen position and normalize it
        Vector2 screenCenter = Camera.main.WorldToScreenPoint(worldCenter);
        screenCenter = new Vector2(screenCenter.x / Screen.width, screenCenter.y / Screen.height);

        // Set the new center for lens distortion
        _lensDistortion.center.value = screenCenter;

        float elapsed = 0f;
        float zoomStep = (zoomLevel - originalZoom) / duration;
        Vector2 centerStep = (screenCenter - originalCenter) / duration;

        while (elapsed < duration)
        {
            token.ThrowIfCancellationRequested();

            _lensDistortion.intensity.value += zoomStep * Time.unscaledDeltaTime;
            _lensDistortion.center.value += centerStep * Time.unscaledDeltaTime;

            elapsed += Time.unscaledDeltaTime;

            await Task.Yield();
        }

        // Enqueue a reset request
        _resetZoomQueue.Enqueue(new ResetZoomRequest(duration), 0f);
    }

    private async Task ZoomResetAsync(float resetDuration, CancellationToken token)
    {
        if (_lensDistortion == null)
        {
            Debug.LogWarning("Lens Distortion effect not found.");
            return;
        }

        float currentZoom = _lensDistortion.intensity.value;
        Vector2 currentCenter = _lensDistortion.center.value;

        float elapsed = 0f;
        float zoomStep = (currentZoom - _originalLensDistortionIntensity) / resetDuration;
        Vector2 centerStep = (currentCenter - _originalLensDistortionCenter) / resetDuration;

        while (elapsed < resetDuration)
        {
            token.ThrowIfCancellationRequested();

            _lensDistortion.intensity.value -= zoomStep * Time.unscaledDeltaTime;
            _lensDistortion.center.value -= centerStep * Time.unscaledDeltaTime;

            elapsed += Time.unscaledDeltaTime;

            await Task.Yield();
        }

        // Reset to original values
        _lensDistortion.intensity.value = _originalLensDistortionIntensity;
        _lensDistortion.center.value = _originalLensDistortionCenter;
    }

    #endregion

    #region ODIN TEST BUTTONS

    [Button("Test Small Shake")]
    private void TestSmallShake() => RequestShake(new CamShakeRequest(0.1f, 0.1f));

    [Button("Test Medium Shake")]
    private void TestMediumShake() => RequestShake(new CamShakeRequest(0.2f, 0.2f));

    [Button("Test Strong Shake")]
    private void TestStrongShake() => RequestShake(new CamShakeRequest(0.5f, 0.2f));

    [Button("Test Multiple Overlapping Shakes")]
    private void TestOverlappingShakes()
    {
        RequestShake(new CamShakeRequest(0.3f, 1.5f));
        RequestShake(new CamShakeRequest(1.5f, 0.5f));
        RequestShake(new CamShakeRequest(0.7f, 0.8f));
    }

    [Button("Test Bloom High Intensity")]
    private void TestBloomHigh() => RequestBloom(new BloomRequest(3f, 2f));

    [Button("Test Bloom Low Intensity")]
    private void TestBloomLow() => RequestBloom(new BloomRequest(1f, 2f));

    [Button("Test Multiple Bloom Requests")]
    private void TestMultipleBloomRequests()
    {
        RequestBloom(new BloomRequest(1f, 3f));
        RequestBloom(new BloomRequest(3f, 1f));
        RequestBloom(new BloomRequest(2f, 2f));
    }

    [Button("Test Small Zoom")]
    private void TestSmallZoom() => RequestZoom(new ZoomRequest(1.2f, 0.5f, Vector3.zero));

    [Button("Test Medium Zoom")]
    private void TestMediumZoom() => RequestZoom(new ZoomRequest(1.5f, 1f, Vector3.zero));

    [Button("Test Strong Zoom")]
    private void TestStrongZoom() => RequestZoom(new ZoomRequest(2f, 1.5f, Vector3.zero));

    [Button("Test Multiple Overlapping Zooms")]
    private void TestOverlappingZooms()
    {
        RequestZoom(new ZoomRequest(1.3f, 2f, Vector3.zero));
        RequestZoom(new ZoomRequest(2.5f, 1f, Vector3.zero));
        RequestZoom(new ZoomRequest(1.8f, 1.2f, Vector3.zero));
    }

    #endregion
}

public struct CamShakeRequest
{
    public float intensity;
    public float duration;

    public CamShakeRequest(float intensity, float duration)
    {
        this.intensity = intensity;
        this.duration = duration;
    }

    public CamShakeRequest(string name)
    {
        var result = EffectManager.Instance.shakeSizes.ToList().Find(x => x.name == name);
        if (result.name == null)
        {
            throw new ArgumentException("Invalid shake size");
        }
        else
        {
            intensity = result.intensity;
            duration = result.duration;
        }
    }
}

public struct BloomRequest
{
    public float intensity;
    public float duration;

    public BloomRequest(float intensity, float duration)
    {
        this.intensity = intensity;
        this.duration = duration;
    }

    public BloomRequest(string name)
    {
        var result = EffectManager.Instance.bloomSizes.ToList().Find(x => x.name == name);
        if (result.name == null)
        {
            throw new ArgumentException("Invalid bloom size");
        }
        else
        {
            intensity = result.intensity;
            duration = result.duration;
        }
    }
}
