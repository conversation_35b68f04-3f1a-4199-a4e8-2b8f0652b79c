using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

public class SceneTransitionManager : MonoBehaviour
{
    public string CombatSceneName, MapSceneName;
    
    public static SceneTransitionManager Instance;

    [Header("Fade Settings")]
    public CanvasGroup fadeCanvasGroup;
    public float fadeDuration = 1f;
    public bool fadeOnStart = true;

    private void Awake()
    {
        // Singleton logic
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Start()
    {
        if (fadeOnStart)
            StartCoroutine(FadeIn());
    }
    public void NewGame()
    {
        LoadScene(MapSceneName);
    }
    public void ExitGame()
    {
        Application.Quit();
    }
    public void LoadScene(string sceneName, bool additive = false)
    {
        PauseManager.Instance.PauseGame();
        StartCoroutine(Transition(sceneName, additive));
    }

    private IEnumerator Transition(string sceneName, bool additive)
    {
        yield return StartCoroutine(FadeOut());

        if (additive)
        {
            yield return SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
        }
        else
        {
            yield return SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Single);
        }

        yield return StartCoroutine(FadeIn());
    }

    private IEnumerator FadeOut()
    {
        fadeCanvasGroup.blocksRaycasts = true;
        float t = 0f;
        while (t < fadeDuration)
        {
            t += Time.deltaTime;
            fadeCanvasGroup.alpha = Mathf.Clamp01(t / fadeDuration);
            yield return null;
        }
    }

    private IEnumerator FadeIn()
    {
        float t = fadeDuration;
        while (t > 0)
        {
            t -= Time.deltaTime;
            fadeCanvasGroup.alpha = Mathf.Clamp01(t / fadeDuration);
            yield return null;
        }
        fadeCanvasGroup.blocksRaycasts = false;
        
        PauseManager.Instance.ResumeGame();
    }
}