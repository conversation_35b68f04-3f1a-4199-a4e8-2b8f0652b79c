using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace EOP.Tetris
{
    public enum MapNodeType
    {
        Shop, Event, Battle
    }
    public class MapNode : MonoBehaviour
    {
        private void Start()
        {
        }

        public virtual void EvokeNodeEvent()
        {
            Debug.Log("EvokeNodeEvent");
            SceneTransitionManager.Instance.LoadScene(SceneTransitionManager.Instance.CombatSceneName);
        }
    }
    
    public class WeightedRandomPicker<T>
    {
        private T[] items;
        private int[] cumulativeWeights;
        private System.Random random;

        public WeightedRandomPicker(T[] items, int[] weights)
        {
            if (items.Length != weights.Length)
                throw new ArgumentException("Items and weights must have the same length.");
        
            this.items = items;
            cumulativeWeights = new int[weights.Length];
            random = new System.Random();
        
            int cumulativeSum = 0;
            for (int i = 0; i < weights.Length; i++)
            {
                cumulativeSum += weights[i];
                cumulativeWeights[i] = cumulativeSum;
            }
        }

        public T PickRandom()
        {
            int rand = random.Next(0, cumulativeWeights.Last());
            for (int i = 0; i < cumulativeWeights.Length; i++)
            {
                if (rand < cumulativeWeights[i])
                    return items[i];
            }
            return default; // Should never reach here
        }
    }
}
