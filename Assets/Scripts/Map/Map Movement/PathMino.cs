using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

namespace EOP.Tetris
{
    /// <summary>
    /// PathMino is a specialized version of Mino designed for path objects.
    /// It provides additional functionality for path-specific behaviors.
    /// </summary>
    public class PathMino : Mino
    {
        [BoxGroup("Path Settings")]
        [LabelText("Path Type")]
        [InfoBox("Type of path this mino represents")]
        public PathMinoType pathMinoType = PathMinoType.Normal;

        [BoxGroup("Path Settings")]
        [LabelText("Movement Cost")]
        [InfoBox("Cost for the player to move through this path")]
        [Range(1, 10)]
        public int movementCost = 1;

        [BoxGroup("Path Settings")]
        [LabelText("Is Directional")]
        [InfoBox("Whether this path has directional restrictions")]
        public bool isDirectional = false;

        [BoxGroup("Path Settings")]
        [LabelText("Allowed Directions")]
        [InfoBox("Directions the player can move through this path")]
        [ShowIf("isDirectional")]
        public List<Direction> allowedDirections = new List<Direction>();

        [BoxGroup("Visual Settings")]
        [LabelText("Path Material")]
        [InfoBox("Special material for this path type")]
        public Material pathMaterial;

        [BoxGroup("Visual Settings")]
        [LabelText("Glow Effect")]
        [InfoBox("Whether this path should glow")]
        public bool hasGlowEffect = false;

        [BoxGroup("Visual Settings")]
        [LabelText("Glow Color")]
        [ShowIf("hasGlowEffect")]
        public Color glowColor = Color.cyan;

        // Private fields
        private PathBehaviour _parentPathBehaviour;
        private bool _isHighlighted = false;
        private Color _originalColor;

        public enum PathMinoType
        {
            Normal,
            Bridge,
            Teleporter,
            OneWay,
            Checkpoint,
            Hazard
        }

        public enum Direction
        {
            Up,
            Down,
            Left,
            Right
        }

        private new void Awake()
        {
            base.Awake();
            _parentPathBehaviour = GetComponentInParent<PathBehaviour>();
            _originalColor = _spriteRenderer.color;
        }

        private new void Start()
        {
            base.Start();
            ApplyPathVisuals();
        }

        /// <summary>
        /// Apply visual effects based on path type
        /// </summary>
        private void ApplyPathVisuals()
        {
            // Apply path material if assigned
            if (pathMaterial != null && _spriteRenderer != null)
            {
                _spriteRenderer.material = pathMaterial;
            }

            // Apply path type specific colors
            ApplyPathTypeColor();

            // Apply glow effect if enabled
            if (hasGlowEffect)
            {
                ApplyGlowEffect();
            }
        }

        /// <summary>
        /// Apply color based on path type
        /// </summary>
        private void ApplyPathTypeColor()
        {
            if (HasSticker) return; // Don't change color if it has a sticker

            Color typeColor = _originalColor;
            
            switch (pathMinoType)
            {
                case PathMinoType.Normal:
                    typeColor = Color.white;
                    break;
                case PathMinoType.Bridge:
                    typeColor = new Color(0.8f, 0.6f, 0.4f); // Brown
                    break;
                case PathMinoType.Teleporter:
                    typeColor = Color.magenta;
                    break;
                case PathMinoType.OneWay:
                    typeColor = Color.yellow;
                    break;
                case PathMinoType.Checkpoint:
                    typeColor = Color.green;
                    break;
                case PathMinoType.Hazard:
                    typeColor = Color.red;
                    break;
            }

            _spriteRenderer.color = typeColor;
            _originalColor = typeColor;
        }

        /// <summary>
        /// Apply glow effect
        /// </summary>
        private void ApplyGlowEffect()
        {
            // This could be enhanced with actual glow shaders
            StartCoroutine(GlowPulse());
        }

        /// <summary>
        /// Glow pulse effect
        /// </summary>
        private IEnumerator GlowPulse()
        {
            while (hasGlowEffect && gameObject.activeInHierarchy)
            {
                float time = 0f;
                float duration = 2f;

                while (time < duration)
                {
                    time += Time.deltaTime;
                    float alpha = Mathf.Lerp(0.5f, 1f, Mathf.PingPong(time, duration / 2f) / (duration / 2f));
                    
                    Color currentColor = _spriteRenderer.color;
                    currentColor.a = alpha;
                    _spriteRenderer.color = currentColor;

                    yield return null;
                }
            }
        }

        /// <summary>
        /// Check if the player can move through this path in the specified direction
        /// </summary>
        public bool CanMoveThrough(Direction direction)
        {
            if (!isDirectional)
                return true;

            return allowedDirections.Contains(direction);
        }

        /// <summary>
        /// Get the movement cost for this path
        /// </summary>
        public int GetMovementCost()
        {
            return movementCost;
        }

        /// <summary>
        /// Highlight this path mino
        /// </summary>
        public void Highlight()
        {
            if (!_isHighlighted)
            {
                _isHighlighted = true;
                _spriteRenderer.color = Color.Lerp(_originalColor, Color.white, 0.5f);
            }
        }

        /// <summary>
        /// Remove highlight from this path mino
        /// </summary>
        public void RemoveHighlight()
        {
            if (_isHighlighted)
            {
                _isHighlighted = false;
                _spriteRenderer.color = _originalColor;
            }
        }

        /// <summary>
        /// Trigger path-specific effects
        /// </summary>
        public override void Trigger()
        {
            base.Trigger();

            switch (pathMinoType)
            {
                case PathMinoType.Teleporter:
                    TriggerTeleporter();
                    break;
                case PathMinoType.Checkpoint:
                    TriggerCheckpoint();
                    break;
                case PathMinoType.Hazard:
                    TriggerHazard();
                    break;
            }
        }

        /// <summary>
        /// Trigger teleporter effect
        /// </summary>
        private void TriggerTeleporter()
        {
            Debug.Log("Teleporter activated!");
            // Add teleporter logic here
        }

        /// <summary>
        /// Trigger checkpoint effect
        /// </summary>
        private void TriggerCheckpoint()
        {
            Debug.Log("Checkpoint reached!");
            // Add checkpoint logic here
        }

        /// <summary>
        /// Trigger hazard effect
        /// </summary>
        private void TriggerHazard()
        {
            Debug.Log("Hazard triggered!");
            // Add hazard logic here
        }

        /// <summary>
        /// Override the color setting to respect path type
        /// </summary>
        public override void SetMinosColor(string color)
        {
            // Only apply color if it's not a special path type
            if (pathMinoType == PathMinoType.Normal)
            {
                base.SetMinosColor(color);
                _originalColor = _spriteRenderer.color;
            }
        }

        #region Testing Methods

        [FoldoutGroup("Path Testing")]
        [Button("Test Path Type Color", ButtonSizes.Medium)]
        [InfoBox("Apply color based on current path type")]
        public void TestPathTypeColor()
        {
            ApplyPathTypeColor();
            Debug.Log($"Applied color for path type: {pathMinoType}");
        }

        [FoldoutGroup("Path Testing")]
        [Button("Toggle Glow Effect", ButtonSizes.Medium)]
        [InfoBox("Toggle the glow effect on/off")]
        public void ToggleGlowEffect()
        {
            hasGlowEffect = !hasGlowEffect;
            if (hasGlowEffect)
            {
                ApplyGlowEffect();
            }
            else
            {
                StopAllCoroutines();
                Color currentColor = _spriteRenderer.color;
                currentColor.a = 1f;
                _spriteRenderer.color = currentColor;
            }
            Debug.Log($"Glow effect: {(hasGlowEffect ? "ON" : "OFF")}");
        }

        [FoldoutGroup("Path Testing")]
        [Button("Test Movement Direction", ButtonSizes.Medium)]
        [InfoBox("Test if movement in all directions is allowed")]
        public void TestMovementDirections()
        {
            Debug.Log($"Movement test for {pathMinoType}:");
            Debug.Log($"Up: {CanMoveThrough(Direction.Up)}");
            Debug.Log($"Down: {CanMoveThrough(Direction.Down)}");
            Debug.Log($"Left: {CanMoveThrough(Direction.Left)}");
            Debug.Log($"Right: {CanMoveThrough(Direction.Right)}");
        }

        #endregion
    }
}
