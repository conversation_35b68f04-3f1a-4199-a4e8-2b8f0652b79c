using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Tetris
{
    /// <summary>
    /// PathBehaviour allows placing path objects on the grid that can be recognized by PlayerMapMoving.
    /// Similar to TetrisMapEntity but specifically designed for path placement with proper grid scaling.
    /// </summary>
    public class PathBehaviour : MonoBehaviour
    {
        [BoxGroup("Path Configuration")]
        [LabelText("Initial Position")]
        [InfoBox("Starting position for this path object")]
        public Vector3 initiatePos;

        [BoxGroup("Path Configuration")]
        [LabelText("Path Prefab")]
        [InfoBox("Prefab to instantiate when this path is placed")]
        public GameObject pathPrefab;

        [BoxGroup("Path Configuration")]
        [LabelText("Path Color")]
        [InfoBox("Color to apply to the path when placed")]
        public Color pathColor = Color.white;

        [BoxGroup("Path Configuration")]
        [LabelText("Path Type")]
        [InfoBox("Type of path for different behaviors")]
        public PathType pathType = PathType.Normal;

        [BoxGroup("Visual Settings")]
        [LabelText("Hover Color")]
        [InfoBox("Color when hovering over the path")]
        public Color hoverColor = Color.yellow;

        [BoxGroup("Visual Settings")]
        [LabelText("Invalid Placement Color")]
        [InfoBox("Color when placement is invalid")]
        public Color invalidColor = Color.red;

        [Header("Runtime Info")]
        [ReadOnly, ShowInInspector]
        [LabelText("Path Components")]
        public Mino[] _pathMinos;

        [ReadOnly, ShowInInspector]
        [LabelText("Is Placed")]
        public bool _isPlaced = false;

        // Private fields
        private Vector3 _offset;
        private Camera _mainCamera;
        private Vector3 _mouseWorldPosition;
        private Vector2Int _mouseUpGridPos;
        private TetrisMapGrid _tetrisMapGrid;
        private bool _isDragging = false;
        private SpriteRenderer[] _spriteRenderers;
        private Color[] _originalColors;

        public enum PathType
        {
            Normal,
            OneWay,
            Bridge,
            Teleporter
        }

        void Start()
        {
            Initialize();
        }

        /// <summary>
        /// Initialize the path behaviour
        /// </summary>
        private void Initialize()
        {
            _mainCamera = Camera.main;
            _pathMinos = GetComponentsInChildren<Mino>();
            _spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
            _tetrisMapGrid = FindObjectOfType<TetrisMapGrid>();

            // Store original colors
            _originalColors = new Color[_spriteRenderers.Length];
            for (int i = 0; i < _spriteRenderers.Length; i++)
            {
                _originalColors[i] = _spriteRenderers[i].color;
            }

            // Set initial position
            transform.position = initiatePos;

            // Scale to match grid size
            ScaleToGrid();

            // Register with manager
            if (PathBehaviourManager.Instance != null)
            {
                PathBehaviourManager.Instance.RegisterPath(this);
            }

            Debug.Log($"PathBehaviour initialized with {_pathMinos.Length} minos at position {transform.position}");
        }

        private void OnDestroy()
        {
            // Unregister from manager
            if (PathBehaviourManager.Instance != null)
            {
                PathBehaviourManager.Instance.UnregisterPath(this);
            }
        }

        /// <summary>
        /// Scale this path object to match the grid size
        /// </summary>
        private void ScaleToGrid()
        {
            if (_tetrisMapGrid != null)
            {
                // Scale all minos to match the grid
                foreach (Mino mino in _pathMinos)
                {
                    if (mino != null)
                    {
                        mino.ScaleToGrid(_tetrisMapGrid);
                    }
                }

                // Ensure the path object itself is properly positioned relative to grid
                Vector2Int gridPos = _tetrisMapGrid.GetGridPos(transform.position);
                Vector3 worldPos = _tetrisMapGrid.GetWorldPos(gridPos);
                transform.position = new Vector3(worldPos.x, worldPos.y, transform.position.z);
            }
        }

        private void Update()
        {
            if (!enabled) return;

            // Handle rotation input
            if (_isDragging)
            {
                if (Input.GetKeyDown(KeyCode.Q))
                {
                    RotatePath(-90f);
                }
                else if (Input.GetKeyDown(KeyCode.E))
                {
                    RotatePath(90f);
                }
            }
        }

        /// <summary>
        /// Rotate the path object
        /// </summary>
        private void RotatePath(float angle)
        {
            transform.Rotate(0, 0, angle);
            UpdateVisualFeedback();
        }

        void OnMouseDown()
        {
            if (!enabled) return;

            // Convert mouse position to world position
            _mouseWorldPosition = _mainCamera.ScreenToWorldPoint(Input.mousePosition);
            _offset = transform.position - new Vector3(_mouseWorldPosition.x, _mouseWorldPosition.y, transform.position.z);

            // Set sorting order to bring to front
            var pathBehaviours = FindObjectsOfType<PathBehaviour>();
            foreach (var sr in _spriteRenderers)
            {
                sr.sortingOrder = pathBehaviours.Length;
            }

            _isDragging = true;

            // If already placed, remove from grid
            if (_isPlaced)
            {
                RemoveFromGrid();
            }
        }

        void OnMouseDrag()
        {
            if (!enabled || !_isDragging) return;

            // Update position to follow mouse
            _mouseWorldPosition = _mainCamera.ScreenToWorldPoint(Input.mousePosition);
            transform.position = new Vector3(_mouseWorldPosition.x, _mouseWorldPosition.y, transform.position.z) + _offset;

            // Update visual feedback
            UpdateVisualFeedback();
        }

        void OnMouseUp()
        {
            if (!enabled || !_isDragging) return;

            _isDragging = false;

            // Check if placement is valid
            if (IsPlacementValid())
            {
                PlaceOnGrid();
            }
            else
            {
                // Reset visual feedback
                ResetColors();
                
                // Reset sorting order
                foreach (var sr in _spriteRenderers)
                {
                    sr.sortingOrder = 0;
                }
            }
        }

        /// <summary>
        /// Check if the current placement is valid
        /// </summary>
        private bool IsPlacementValid()
        {
            bool hasCollision = false;
            bool outOfGrid = false;

            foreach (Mino mino in _pathMinos)
            {
                Vector2Int gridPos = _tetrisMapGrid.GetGridPos(mino.transform.position);
                
                // Check if out of grid
                if (!_tetrisMapGrid.IsWithinGrid(gridPos))
                {
                    outOfGrid = true;
                    break;
                }

                // Check if position already has a path
                if (_tetrisMapGrid.HasPath(gridPos))
                {
                    hasCollision = true;
                    break;
                }
            }

            return !hasCollision && !outOfGrid;
        }

        /// <summary>
        /// Update visual feedback based on placement validity
        /// </summary>
        private void UpdateVisualFeedback()
        {
            if (!_isDragging) return;

            Color feedbackColor = IsPlacementValid() ? hoverColor : invalidColor;
            
            foreach (var sr in _spriteRenderers)
            {
                sr.color = feedbackColor;
            }
        }

        /// <summary>
        /// Place the path on the grid
        /// </summary>
        private void PlaceOnGrid()
        {
            // Add all minos to the grid as paths
            foreach (Mino mino in _pathMinos)
            {
                _tetrisMapGrid.AddToGrid(mino);
            }

            // Snap to grid position
            _mouseUpGridPos = _tetrisMapGrid.GetGridPos(transform.position);
            Vector3 worldPos = _tetrisMapGrid.GetWorldPos(_mouseUpGridPos);
            transform.position = new Vector3(worldPos.x, worldPos.y, transform.position.z);

            // Set placed state
            _isPlaced = true;

            // Notify manager
            if (PathBehaviourManager.Instance != null)
            {
                PathBehaviourManager.Instance.MarkPathAsPlaced(this);
            }

            // Apply path color
            foreach (var sr in _spriteRenderers)
            {
                sr.color = pathColor;
                sr.sortingOrder = 0;
            }

            // Create new path instance if prefab is assigned
            if (pathPrefab != null)
            {
                InstantiateNewPath();
            }

            Debug.Log($"Path placed at grid position {_mouseUpGridPos}");
        }

        /// <summary>
        /// Remove the path from the grid
        /// </summary>
        private void RemoveFromGrid()
        {
            if (!_isPlaced) return;

            foreach (Mino mino in _pathMinos)
            {
                Vector2Int gridPos = _tetrisMapGrid.GetGridPos(mino.transform.position);
                _tetrisMapGrid.RemoveFromGrid(gridPos);
            }

            _isPlaced = false;

            // Notify manager
            if (PathBehaviourManager.Instance != null)
            {
                PathBehaviourManager.Instance.MarkPathAsRemoved(this);
            }

            Debug.Log("Path removed from grid");
        }

        /// <summary>
        /// Reset colors to original
        /// </summary>
        private void ResetColors()
        {
            for (int i = 0; i < _spriteRenderers.Length && i < _originalColors.Length; i++)
            {
                _spriteRenderers[i].color = _originalColors[i];
            }
        }

        /// <summary>
        /// Create a new path instance
        /// </summary>
        private void InstantiateNewPath()
        {
            if (pathPrefab != null)
            {
                GameObject newPath = Instantiate(pathPrefab);
                PathBehaviour newPathBehaviour = newPath.GetComponent<PathBehaviour>();
                
                if (newPathBehaviour != null)
                {
                    // Copy settings to new instance
                    newPathBehaviour.pathColor = pathColor;
                    newPathBehaviour.pathType = pathType;
                }
            }

            // Disable this instance since it's now placed
            enabled = false;
        }

        #region Testing Methods

        [FoldoutGroup("Testing")]
        [Button("Force Scale to Grid", ButtonSizes.Medium)]
        [InfoBox("Manually scale this path to match the grid")]
        public void ForceScaleToGrid()
        {
            ScaleToGrid();
            Debug.Log($"Scaled path to grid size {(_tetrisMapGrid != null ? _tetrisMapGrid.gridSize : 0)}");
        }

        [FoldoutGroup("Testing")]
        [Button("Test Placement Validity", ButtonSizes.Medium)]
        [InfoBox("Check if current position is valid for placement")]
        public void TestPlacementValidity()
        {
            bool isValid = IsPlacementValid();
            Debug.Log($"Current placement is {(isValid ? "VALID" : "INVALID")}");
        }

        [FoldoutGroup("Testing")]
        [Button("Snap to Grid", ButtonSizes.Medium)]
        [InfoBox("Snap this path to the nearest grid position")]
        public void SnapToGrid()
        {
            if (_tetrisMapGrid != null)
            {
                Vector2Int gridPos = _tetrisMapGrid.GetGridPos(transform.position);
                Vector3 worldPos = _tetrisMapGrid.GetWorldPos(gridPos);
                transform.position = new Vector3(worldPos.x, worldPos.y, transform.position.z);
                Debug.Log($"Snapped to grid position {gridPos}");
            }
        }

        #endregion
    }
}
