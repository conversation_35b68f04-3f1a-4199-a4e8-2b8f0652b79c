using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Tetris
{
    public class PlayerMapMoving : MonoBehaviour
    {
        public TetrisMapGrid mapGrid;
        [ShowInInspector, ReadOnly] private Vector2Int _mapGridPos;
        public bool CanMove = true;

        private void Start()
        {
            EventBetter.Listen(this, (PauseEvent p) => { CanMove = false; });
            EventBetter.Listen(this, (ResumeEvent r) => { CanMove = true; });
        }

        private void Update()
        {
            UpdateGridPos(mapGrid.GetGridPos(transform.position));
            Movement();
        }

        private void Movement()
        {
            if (!CanMove)
                return;

            if (UnityEngine.Input.GetKeyDown(KeyCode.A))
            {
                transform.Translate(Vector3.left * mapGrid.gridSize);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.right * mapGrid.gridSize);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.D))
            {
                transform.Translate(Vector3.right * mapGrid.gridSize);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.left * mapGrid.gridSize);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.W))
            {
                transform.Translate(Vector3.up * mapGrid.gridSize);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.down * mapGrid.gridSize);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.S))
            {
                transform.Translate(Vector3.down * mapGrid.gridSize);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.up * mapGrid.gridSize);
                }
            }
        }

        private bool IsPosLegal()
        {
            Vector2Int gridPos = mapGrid.GetGridPos(transform.position);

            if (mapGrid.IsWithinGrid(gridPos))
            {
                // Check for map nodes first (highest priority)
                MapNode node = mapGrid.HasMapNode(gridPos);
                if (node)
                {
                    node.EvokeNodeEvent();
                    return true;
                }

                // Check for paths (including PathBehaviour paths)
                if (mapGrid.HasPath(gridPos))
                {
                    // Check if there's a PathMino at this position for special effects
                    CheckPathMinoEffects(gridPos);
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Check for special effects from PathMino objects at the given position
        /// </summary>
        private void CheckPathMinoEffects(Vector2Int gridPos)
        {
            // Find all PathBehaviour objects in the scene
            PathBehaviour[] pathBehaviours = FindObjectsOfType<PathBehaviour>();

            foreach (PathBehaviour pathBehaviour in pathBehaviours)
            {
                if (pathBehaviour._isPlaced)
                {
                    foreach (Mino mino in pathBehaviour._pathMinos)
                    {
                        Vector2Int minoGridPos = mapGrid.GetGridPos(mino.transform.position);
                        if (minoGridPos == gridPos)
                        {
                            // Trigger path-specific effects
                            if (mino is PathMino pathMino)
                            {
                                HandlePathMinoInteraction(pathMino);
                            }
                            else
                            {
                                // Regular mino on path
                                mino.Trigger();
                            }
                            return;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Handle interaction with PathMino objects
        /// </summary>
        private void HandlePathMinoInteraction(PathMino pathMino)
        {
            // Get movement direction (this could be enhanced to track actual movement direction)
            Vector2Int currentPos = mapGrid.GetGridPos(transform.position);

            // For now, assume the player can move through the path
            // This could be enhanced to check actual movement direction
            bool canMove = pathMino.CanMoveThrough(PathMino.Direction.Up); // Placeholder

            if (canMove)
            {
                pathMino.Trigger();

                // Apply movement cost if needed
                int cost = pathMino.GetMovementCost();
                if (cost > 1)
                {
                    Debug.Log($"Movement cost: {cost}");
                    // Here you could implement movement cost logic
                }
            }
            else
            {
                Debug.Log("Movement blocked by directional path restriction");
            }
        }

        public void UpdateGridPos(Vector2Int gridPos)
        {
            _mapGridPos = gridPos;
            transform.position = mapGrid.GetWorldPos(gridPos);
        }
    }
}