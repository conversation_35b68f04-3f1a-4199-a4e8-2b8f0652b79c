using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

namespace EOP.Tetris
{
    /// <summary>
    /// PathPrefabCreator helps create and configure path prefabs with proper components and settings.
    /// This is a utility class for setting up PathBehaviour objects in the editor.
    /// </summary>
    public class PathPrefabCreator : MonoBehaviour
    {
        [BoxGroup("Prefab Configuration")]
        [LabelText("Path Shape")]
        [InfoBox("Select the shape pattern for this path")]
        public PathShape pathShape = PathShape.Single;

        [BoxGroup("Prefab Configuration")]
        [LabelText("Path Type")]
        [InfoBox("Type of path functionality")]
        public PathBehaviour.PathType pathType = PathBehaviour.PathType.Normal;

        [BoxGroup("Prefab Configuration")]
        [LabelText("Mino Prefab")]
        [InfoBox("Base mino prefab to use for creating path pieces")]
        public GameObject minoPrefab;

        [BoxGroup("Visual Settings")]
        [LabelText("Path Color")]
        public Color pathColor = Color.white;

        [BoxGroup("Visual Settings")]
        [LabelText("Use Path Mino")]
        [InfoBox("Use PathMino instead of regular Mino for advanced features")]
        public bool usePathMino = true;

        [BoxGroup("Visual Settings")]
        [LabelText("Path Mino Type")]
        [ShowIf("usePathMino")]
        public PathMino.PathMinoType pathMinoType = PathMino.PathMinoType.Normal;

        public enum PathShape
        {
            Single,      // 1x1
            Line2,       // 1x2 line
            Line3,       // 1x3 line
            Line4,       // 1x4 line
            LShape,      // L-shaped path
            TShape,      // T-shaped path
            Cross,       // + shaped path
            Square2x2,   // 2x2 square
            Custom       // Custom shape defined by positions
        }

        [BoxGroup("Custom Shape")]
        [ShowIf("pathShape", PathShape.Custom)]
        [LabelText("Custom Positions")]
        [InfoBox("Define custom mino positions relative to the center")]
        public Vector2Int[] customPositions = new Vector2Int[] { Vector2Int.zero };

        #region Prefab Creation Methods

        [FoldoutGroup("Prefab Creation")]
        [Button("Create Path Prefab", ButtonSizes.Large)]
        [InfoBox("Create a new path prefab with the specified configuration")]
        public void CreatePathPrefab()
        {
            if (minoPrefab == null)
            {
                Debug.LogError("Mino prefab is required to create path prefab!");
                return;
            }

            // Create main path object
            GameObject pathObject = new GameObject($"Path_{pathShape}_{pathType}");
            pathObject.transform.position = transform.position;

            // Add PathBehaviour component
            PathBehaviour pathBehaviour = pathObject.AddComponent<PathBehaviour>();
            pathBehaviour.pathType = pathType;
            pathBehaviour.pathColor = pathColor;
            pathBehaviour.initiatePos = transform.position;

            // Get positions for the selected shape
            Vector2Int[] positions = GetPositionsForShape(pathShape);

            // Create minos for each position
            for (int i = 0; i < positions.Length; i++)
            {
                CreateMinoAtPosition(pathObject, positions[i], i);
            }

            // Configure the PathBehaviour
            pathBehaviour._pathMinos = pathObject.GetComponentsInChildren<Mino>();

            Debug.Log($"Created path prefab '{pathObject.name}' with {positions.Length} minos");

            // Select the created object in the hierarchy
#if UNITY_EDITOR
            UnityEditor.Selection.activeGameObject = pathObject;
#endif
        }

        /// <summary>
        /// Create a mino at the specified position
        /// </summary>
        private void CreateMinoAtPosition(GameObject parent, Vector2Int position, int index)
        {
            GameObject minoObject = Instantiate(minoPrefab, parent.transform);
            minoObject.name = $"PathMino_{index}";
            minoObject.transform.localPosition = new Vector3(position.x, position.y, 0);

            // Replace Mino component with PathMino if requested
            if (usePathMino)
            {
                Mino originalMino = minoObject.GetComponent<Mino>();
                if (originalMino != null)
                {
                    // Store original values
                    bool hasSticker = originalMino.HasSticker;
                    
                    // Remove original component
                    DestroyImmediate(originalMino);
                    
                    // Add PathMino component
                    PathMino pathMino = minoObject.AddComponent<PathMino>();
                    pathMino.HasSticker = hasSticker;
                    pathMino.pathMinoType = pathMinoType;
                }
            }

            // Set color
            SpriteRenderer spriteRenderer = minoObject.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                spriteRenderer.color = pathColor;
            }
        }

        /// <summary>
        /// Get positions for the specified shape
        /// </summary>
        private Vector2Int[] GetPositionsForShape(PathShape shape)
        {
            switch (shape)
            {
                case PathShape.Single:
                    return new Vector2Int[] { Vector2Int.zero };

                case PathShape.Line2:
                    return new Vector2Int[] { Vector2Int.zero, Vector2Int.right };

                case PathShape.Line3:
                    return new Vector2Int[] { Vector2Int.left, Vector2Int.zero, Vector2Int.right };

                case PathShape.Line4:
                    return new Vector2Int[] { 
                        new Vector2Int(-1, 0), Vector2Int.zero, 
                        Vector2Int.right, new Vector2Int(2, 0) 
                    };

                case PathShape.LShape:
                    return new Vector2Int[] { 
                        Vector2Int.zero, Vector2Int.right, 
                        Vector2Int.up 
                    };

                case PathShape.TShape:
                    return new Vector2Int[] { 
                        Vector2Int.left, Vector2Int.zero, Vector2Int.right, 
                        Vector2Int.up 
                    };

                case PathShape.Cross:
                    return new Vector2Int[] { 
                        Vector2Int.zero, Vector2Int.up, Vector2Int.down, 
                        Vector2Int.left, Vector2Int.right 
                    };

                case PathShape.Square2x2:
                    return new Vector2Int[] { 
                        Vector2Int.zero, Vector2Int.right, 
                        Vector2Int.up, new Vector2Int(1, 1) 
                    };

                case PathShape.Custom:
                    return customPositions ?? new Vector2Int[] { Vector2Int.zero };

                default:
                    return new Vector2Int[] { Vector2Int.zero };
            }
        }

        [FoldoutGroup("Prefab Creation")]
        [Button("Create Multiple Shapes", ButtonSizes.Medium)]
        [InfoBox("Create prefabs for all basic shapes")]
        public void CreateAllBasicShapes()
        {
            if (minoPrefab == null)
            {
                Debug.LogError("Mino prefab is required!");
                return;
            }

            PathShape[] basicShapes = {
                PathShape.Single, PathShape.Line2, PathShape.Line3, PathShape.Line4,
                PathShape.LShape, PathShape.TShape, PathShape.Cross, PathShape.Square2x2
            };

            Vector3 startPosition = transform.position;
            float spacing = 3f;

            for (int i = 0; i < basicShapes.Length; i++)
            {
                pathShape = basicShapes[i];
                transform.position = startPosition + Vector3.right * (i * spacing);
                CreatePathPrefab();
            }

            transform.position = startPosition;
            Debug.Log($"Created {basicShapes.Length} basic path shapes");
        }

        [FoldoutGroup("Prefab Creation")]
        [Button("Preview Shape", ButtonSizes.Medium)]
        [InfoBox("Preview the selected shape in the scene view")]
        public void PreviewShape()
        {
            Vector2Int[] positions = GetPositionsForShape(pathShape);
            
            Debug.Log($"Shape: {pathShape}");
            Debug.Log($"Positions: {string.Join(", ", positions)}");
            
            // Draw gizmos for preview
            for (int i = 0; i < positions.Length; i++)
            {
                Vector3 worldPos = transform.position + new Vector3(positions[i].x, positions[i].y, 0);
                Debug.DrawRay(worldPos, Vector3.up * 0.5f, Color.green, 2f);
                Debug.DrawRay(worldPos, Vector3.right * 0.5f, Color.green, 2f);
            }
        }

        #endregion

        #region Gizmos

        private void OnDrawGizmos()
        {
            if (pathShape == PathShape.Custom && customPositions != null)
            {
                Gizmos.color = pathColor;
                foreach (Vector2Int pos in customPositions)
                {
                    Vector3 worldPos = transform.position + new Vector3(pos.x, pos.y, 0);
                    Gizmos.DrawWireCube(worldPos, Vector3.one * 0.8f);
                }
            }
            else
            {
                Vector2Int[] positions = GetPositionsForShape(pathShape);
                Gizmos.color = pathColor;
                foreach (Vector2Int pos in positions)
                {
                    Vector3 worldPos = transform.position + new Vector3(pos.x, pos.y, 0);
                    Gizmos.DrawWireCube(worldPos, Vector3.one * 0.8f);
                }
            }
        }

        private void OnDrawGizmosSelected()
        {
            Vector2Int[] positions = GetPositionsForShape(pathShape);
            Gizmos.color = Color.yellow;
            foreach (Vector2Int pos in positions)
            {
                Vector3 worldPos = transform.position + new Vector3(pos.x, pos.y, 0);
                Gizmos.DrawCube(worldPos, Vector3.one * 0.9f);
            }
        }

        #endregion
    }
}
