using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Tetris
{
    /// <summary>
    /// PathBehaviourManager manages all PathBehaviour objects in the scene and provides utilities
    /// for path management, validation, and interaction with the grid system.
    /// </summary>
    public class PathBehaviourManager : MonoBehaviour
    {
        [BoxGroup("Manager Settings")]
        [LabelText("Auto-Register Paths")]
        [InfoBox("Automatically register PathBehaviour objects when they are created")]
        public bool autoRegisterPaths = true;

        [BoxGroup("Manager Settings")]
        [LabelText("Grid Reference")]
        [InfoBox("Reference to the TetrisMapGrid")]
        public TetrisMapGrid mapGrid;

        [BoxGroup("Path Management")]
        [LabelText("Registered Paths")]
        [InfoBox("All PathBehaviour objects currently registered with this manager")]
        [ReadOnly, ShowInInspector]
        public List<PathBehaviour> registeredPaths = new List<PathBehaviour>();

        [BoxGroup("Path Management")]
        [LabelText("Placed Paths")]
        [InfoBox("PathBehaviour objects that have been placed on the grid")]
        [ReadOnly, ShowInInspector]
        public List<PathBehaviour> placedPaths = new List<PathBehaviour>();

        [BoxGroup("Statistics")]
        [LabelText("Total Path Count")]
        [ReadOnly, ShowInInspector]
        public int TotalPathCount => registeredPaths.Count;

        [BoxGroup("Statistics")]
        [LabelText("Placed Path Count")]
        [ReadOnly, ShowInInspector]
        public int PlacedPathCount => placedPaths.Count;

        [BoxGroup("Statistics")]
        [LabelText("Available Path Count")]
        [ReadOnly, ShowInInspector]
        public int AvailablePathCount => registeredPaths.Count - placedPaths.Count;

        // Singleton instance
        public static PathBehaviourManager Instance { get; private set; }

        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Find grid if not assigned
            if (mapGrid == null)
            {
                mapGrid = FindObjectOfType<TetrisMapGrid>();
            }
        }

        private void Start()
        {
            // Auto-register existing paths if enabled
            if (autoRegisterPaths)
            {
                RegisterAllExistingPaths();
            }
        }

        /// <summary>
        /// Register a PathBehaviour with this manager
        /// </summary>
        public void RegisterPath(PathBehaviour pathBehaviour)
        {
            if (pathBehaviour != null && !registeredPaths.Contains(pathBehaviour))
            {
                registeredPaths.Add(pathBehaviour);
                Debug.Log($"Registered path: {pathBehaviour.name}");
            }
        }

        /// <summary>
        /// Unregister a PathBehaviour from this manager
        /// </summary>
        public void UnregisterPath(PathBehaviour pathBehaviour)
        {
            if (pathBehaviour != null)
            {
                registeredPaths.Remove(pathBehaviour);
                placedPaths.Remove(pathBehaviour);
                Debug.Log($"Unregistered path: {pathBehaviour.name}");
            }
        }

        /// <summary>
        /// Mark a path as placed
        /// </summary>
        public void MarkPathAsPlaced(PathBehaviour pathBehaviour)
        {
            if (pathBehaviour != null && registeredPaths.Contains(pathBehaviour) && !placedPaths.Contains(pathBehaviour))
            {
                placedPaths.Add(pathBehaviour);
                Debug.Log($"Path placed: {pathBehaviour.name}");
            }
        }

        /// <summary>
        /// Mark a path as removed from grid
        /// </summary>
        public void MarkPathAsRemoved(PathBehaviour pathBehaviour)
        {
            if (pathBehaviour != null)
            {
                placedPaths.Remove(pathBehaviour);
                Debug.Log($"Path removed: {pathBehaviour.name}");
            }
        }

        /// <summary>
        /// Register all existing PathBehaviour objects in the scene
        /// </summary>
        private void RegisterAllExistingPaths()
        {
            PathBehaviour[] existingPaths = FindObjectsOfType<PathBehaviour>();
            foreach (PathBehaviour path in existingPaths)
            {
                RegisterPath(path);
                
                // Check if already placed
                if (path._isPlaced)
                {
                    MarkPathAsPlaced(path);
                }
            }
            
            Debug.Log($"Auto-registered {existingPaths.Length} existing paths");
        }

        /// <summary>
        /// Get all paths of a specific type
        /// </summary>
        public List<PathBehaviour> GetPathsByType(PathBehaviour.PathType pathType)
        {
            return registeredPaths.Where(p => p.pathType == pathType).ToList();
        }

        /// <summary>
        /// Get all placed paths of a specific type
        /// </summary>
        public List<PathBehaviour> GetPlacedPathsByType(PathBehaviour.PathType pathType)
        {
            return placedPaths.Where(p => p.pathType == pathType).ToList();
        }

        /// <summary>
        /// Find the closest path to a given position
        /// </summary>
        public PathBehaviour FindClosestPath(Vector3 position, bool onlyPlaced = true)
        {
            List<PathBehaviour> pathsToSearch = onlyPlaced ? placedPaths : registeredPaths;
            
            PathBehaviour closestPath = null;
            float closestDistance = float.MaxValue;

            foreach (PathBehaviour path in pathsToSearch)
            {
                float distance = Vector3.Distance(position, path.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestPath = path;
                }
            }

            return closestPath;
        }

        /// <summary>
        /// Validate all placed paths (check if they're still valid on the grid)
        /// </summary>
        public void ValidateAllPlacedPaths()
        {
            List<PathBehaviour> invalidPaths = new List<PathBehaviour>();

            foreach (PathBehaviour path in placedPaths)
            {
                if (path == null || !IsPathValidOnGrid(path))
                {
                    invalidPaths.Add(path);
                }
            }

            // Remove invalid paths
            foreach (PathBehaviour invalidPath in invalidPaths)
            {
                if (invalidPath != null)
                {
                    MarkPathAsRemoved(invalidPath);
                }
                else
                {
                    placedPaths.Remove(invalidPath);
                }
            }

            if (invalidPaths.Count > 0)
            {
                Debug.LogWarning($"Removed {invalidPaths.Count} invalid paths from placed paths list");
            }
        }

        /// <summary>
        /// Check if a path is valid on the grid
        /// </summary>
        private bool IsPathValidOnGrid(PathBehaviour path)
        {
            if (path == null || path._pathMinos == null || mapGrid == null)
                return false;

            foreach (Mino mino in path._pathMinos)
            {
                if (mino == null)
                    continue;

                Vector2Int gridPos = mapGrid.GetGridPos(mino.transform.position);
                if (!mapGrid.IsWithinGrid(gridPos))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Clear all paths from the grid
        /// </summary>
        public void ClearAllPaths()
        {
            foreach (PathBehaviour path in placedPaths.ToList())
            {
                if (path != null)
                {
                    // Remove from grid
                    foreach (Mino mino in path._pathMinos)
                    {
                        Vector2Int gridPos = mapGrid.GetGridPos(mino.transform.position);
                        mapGrid.RemoveFromGrid(gridPos);
                    }
                    
                    MarkPathAsRemoved(path);
                }
            }
            
            Debug.Log("Cleared all paths from grid");
        }

        #region Testing and Debug Methods

        [FoldoutGroup("Manager Testing")]
        [Button("Refresh Path Registry", ButtonSizes.Medium)]
        [InfoBox("Re-scan and register all PathBehaviour objects in the scene")]
        public void RefreshPathRegistry()
        {
            registeredPaths.Clear();
            placedPaths.Clear();
            RegisterAllExistingPaths();
        }

        [FoldoutGroup("Manager Testing")]
        [Button("Validate All Paths", ButtonSizes.Medium)]
        [InfoBox("Check all placed paths for validity")]
        public void TestValidateAllPaths()
        {
            ValidateAllPlacedPaths();
            Debug.Log($"Validation complete. {PlacedPathCount} valid paths remaining.");
        }

        [FoldoutGroup("Manager Testing")]
        [Button("Log Path Statistics", ButtonSizes.Medium)]
        [InfoBox("Log detailed statistics about paths")]
        public void LogPathStatistics()
        {
            Debug.Log($"=== Path Statistics ===");
            Debug.Log($"Total Registered: {TotalPathCount}");
            Debug.Log($"Placed: {PlacedPathCount}");
            Debug.Log($"Available: {AvailablePathCount}");
            
            foreach (PathBehaviour.PathType pathType in System.Enum.GetValues(typeof(PathBehaviour.PathType)))
            {
                int count = GetPathsByType(pathType).Count;
                int placedCount = GetPlacedPathsByType(pathType).Count;
                Debug.Log($"{pathType}: {placedCount}/{count} placed");
            }
        }

        [FoldoutGroup("Manager Testing")]
        [Button("Clear All Paths", ButtonSizes.Medium)]
        [InfoBox("Remove all paths from the grid")]
        public void TestClearAllPaths()
        {
            ClearAllPaths();
        }

        #endregion
    }
}
