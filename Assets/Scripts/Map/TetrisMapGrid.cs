using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Tetris
{
    public class TetrisMapGrid : TetrisGrid
    {
        [ReadOnly, ShowInInspector] 
        public bool[,] PathMinos;
        public MapNode[,] MapNodes;
        protected override void Awake()
        {
            base.Awake();
            
            PathMinos = new bool[boardSize.x, boardSize.y];
            MapNodes = new MapNode[boardSize.x, boardSize.y];
        }

        public void AddToGrid(Mino mino)
        {
            var gridPos = GetGridPos(mino.transform.position);
            if (IsWithinGrid(gridPos))
                PathMinos[gridPos.x, gridPos.y] = true;
        }

        public void RemoveFromGrid(Vector2Int gridPos)
        {
            if (IsWithinGrid(gridPos))
                PathMinos[gridPos.x, gridPos.y] = false;
        }

        public bool HasPath(Vector2Int gridPos)
        {
            if (IsWithinGrid(gridPos))
                return PathMinos[gridPos.x, gridPos.y];
            return false;
        }

        public MapNode HasMapNode(Vector2Int gridPos)
        {
            if (IsWithinGrid(gridPos))
            {
                return MapNodes[gridPos.x, gridPos.y];
            }

            return null;
        }
        public bool IsWithinGrid(Vector2Int gridPos)
        {
            return gridPos.x < GridMinos.GetLength(0) && gridPos.y < GridMinos.GetLength(1) && gridPos.x >= 0 &&
                   gridPos.y >= 0;
        }
    }
}
