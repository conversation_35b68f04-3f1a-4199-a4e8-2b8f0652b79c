using UnityEngine;
using MoreMountains.Feedbacks;
using Sirenix.OdinInspector;

namespace EOP.combat
{
    public class CombatScreenShake : MonoBehaviour
    {
        [Header("屏幕震动设置")]
        [Tooltip("轻微震动 - 用于UI反馈")]
        public float lightShakeDuration = 0.2f;
        public float lightShakeAmplitude = 0.05f;
        public float lightShakeFrequency = 0.1f;
        
        [<PERSON><PERSON><PERSON>("中等震动 - 用于攻击命中")]
        public float mediumShakeDuration = 0.3f;
        public float mediumShakeAmplitude = 0.08f;
        public float mediumShakeFrequency = 0.1f;
        
        [Tooltip("强烈震动 - 用于爆炸/死亡")]
        public float heavyShakeDuration = 0.5f;
        public float heavyShakeAmplitude = 0.15f;
        public float heavyShakeFrequency = 0.15f;
        
        [Toolt<PERSON>("致命震动 - 用于Boss攻击")]
        public float deadlyShakeDuration = 1.0f;
        public float deadlyShakeAmplitude = 0.25f;
        public float deadlyShakeFrequency = 0.2f;
        
        /// <summary>
        /// 轻微震动 - UI反馈
        /// </summary>
        [But<PERSON>("轻微震动")]
        public void LightShake()
        {
            MMCameraShakeEvent.Trigger(lightShakeDuration, lightShakeAmplitude, lightShakeFrequency, 0f, 0f, 0f);
        }
        
        /// <summary>
        /// 中等震动 - 攻击命中
        /// </summary>
        public void MediumShake()
        {
            MMCameraShakeEvent.Trigger(mediumShakeDuration, mediumShakeAmplitude, mediumShakeFrequency, 0f, 0f, 0f);
        }
        
        /// <summary>
        /// 强烈震动 - 爆炸/死亡
        /// </summary>
        public void HeavyShake()
        {
            MMCameraShakeEvent.Trigger(heavyShakeDuration, heavyShakeAmplitude, heavyShakeFrequency, 0f, 0f, 0f);
        }
        
        /// <summary>
        /// 致命震动 - Boss攻击
        /// </summary>
        [Button("致命震动")]
        public void DeadlyShake()
        {
            MMCameraShakeEvent.Trigger(deadlyShakeDuration, deadlyShakeAmplitude, deadlyShakeFrequency, 0f, 0f, 0f);
        }
        
        /// <summary>
        /// 自定义震动
        /// </summary>
        /// <param name="duration">持续时间</param>
        /// <param name="amplitude">幅度</param>
        /// <param name="frequency">频率</param>
        public void CustomShake(float duration, float amplitude, float frequency)
        {
            MMCameraShakeEvent.Trigger(duration, amplitude, frequency, 0f, 0f, 0f);
        }
        
        // 测试方法
        [ContextMenu("测试轻微震动")]
        public void TestLightShake()
        {
            LightShake();
        }
        
        [ContextMenu("测试中等震动")]
        public void TestMediumShake()
        {
            MediumShake();
        }
        
        [ContextMenu("测试强烈震动")]
        public void TestHeavyShake()
        {
            HeavyShake();
        }
        
        [ContextMenu("测试致命震动")]
        public void TestDeadlyShake()
        {
            DeadlyShake();
        }
    }
}