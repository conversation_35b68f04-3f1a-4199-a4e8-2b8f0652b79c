using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace EOP.combat
{
    public class CombatEnemyInterface : MonoBehaviour
    {
        private CombatManager _combatManager;
        public int _currentHP;
        public int _maxHP;
        public Dictionary<CombatBuff, int> _buffs;
        public int _actionsLeft;
        public string _nextAction;
        public CombatEnemyUI combatEnemyUI;
        public Dictionary<string, int> _actionBank = new Dictionary<string, int>()
        {
            {"Attack", 5},
            {"Heal", 1},
            {"Strength", 1},
        };
        public int basicAttackDamage = 4;
        public int actualAttackDamage;
        public int basicHealAmount = 3;
        public bool targeted;
       
#region initialization
        // Finds the combat manager in the scene when the scene starts every time
        void Awake()
        {
            _combatManager = FindObjectOfType<CombatManager>();
            _buffs = new Dictionary<CombatBuff, int>();
            combatEnemyUI = GetComponent<CombatEnemyUI>();
            actualAttackDamage = basicAttackDamage;
            CalculateActualAttackDamage();
        }

        //click on the
        void OnMouseDown()
            {
                if (UnityEngine.Input.GetMouseButtonDown(0)) // Left mouse button
                {
                    foreach (CombatEnemyInterface enemy in _combatManager._combatEnemies)
                    {
                        enemy.targeted = false;
                    }
                    targeted = true;
                    _combatManager.target = this;
                    Debug.Log($"{name} is the target now.");
                    _combatManager.UpdateUIs();
                }
            }
#endregion
#region main functions

        // Change the action left
        public void ChangeActionLeft(int amount)
        {
            //Debug.Log($"{name} has triggered ChangeActionLeft with amount {amount}");
             _actionsLeft += amount;
            if (_actionsLeft <= 0)
            {
                _actionsLeft = 0;
                ExecuteAction();
                
                // 从技能管理器获取下一个技能的行动计数
                Component skillManager = GetComponent("EnemySkillManager");
                if (skillManager != null)
                {
                    var currentActionCountField = skillManager.GetType().GetField("currentActionCount");
                    if (currentActionCountField != null)
                    {
                        var actionCount = currentActionCountField.GetValue(skillManager);
                        _actionsLeft = actionCount != null ? (int)actionCount : 3;
                    }
                    else
                    {
                        _actionsLeft = 3; // 默认值
                    }
                }
                else
                {
                    _actionsLeft = 3; // 默认值
                }
            }
            //Debug.Log($"{name} has {_actionsLeft} actions left");
            //Debug.Log(combatEnemyUI.gameObject.name);
            combatEnemyUI.UpdateEnemyUI();  
        }
        public void ExecuteAction()
        {
            CalculateActualAttackDamage(); // for double check
            
            // 使用技能系统
            Component skillManager = GetComponent("EnemySkillManager");
            if (skillManager != null)
            {
                // 通过反射调用ExecuteCurrentSkill方法
                skillManager.GetType().GetMethod("ExecuteCurrentSkill")?.Invoke(skillManager, null);
                // 通过反射调用SelectNextSkill方法
                skillManager.GetType().GetMethod("SelectNextSkill")?.Invoke(skillManager, null);
                // 更新_nextAction显示
                var getCurrentSkillInfoMethod = skillManager.GetType().GetMethod("GetCurrentSkillInfo");
                if (getCurrentSkillInfoMethod != null)
                {
                    _nextAction = getCurrentSkillInfoMethod.Invoke(skillManager, null)?.ToString() ?? "Unknown";
                }
            }
            else
            {
                Debug.LogWarning($"{name} does not have EnemySkillManager component!");
            }
            
            CalculateActualAttackDamage(); // for double check
            combatEnemyUI.UpdateEnemyUI();  
        }

        private void _ExecuteAction()
        {
            if (_nextAction == "Attack")
            {
                AttackPlayer();
                ReduceBuffLayer();
            }
            else if (_nextAction == "Heal")
            {
                ChangeHP(basicHealAmount);
            }
            else if (_nextAction == "Strength")
            {
                AddBuff(new StrengthBuff());
            }
            else if (_nextAction == "Weakness")
            {
                AddBuff(new WeaknessBuff());
            }
        }

        // Randomly select an action from the action bank based on the weights, which are the values in the dictionary
        public void SetNextAction()
        {
            int totalWeight = 0;
            foreach (KeyValuePair<string, int> action in _actionBank)
            {
                totalWeight += action.Value;
            }
            int randomWeight = UnityEngine.Random.Range(0, totalWeight);
            foreach (KeyValuePair<string, int> action in _actionBank)
            {
                randomWeight -= action.Value;
                if (randomWeight <= 0)
                {
                    _nextAction = action.Key;
                    break;
                }
            }
        }

        
        
#endregion
#region Buff related
        // calculate actual attack damage based on the buffs
        public void CalculateActualAttackDamage()
        {
            float dummy = (float )basicAttackDamage;
            foreach (KeyValuePair<CombatBuff, int> buffPair in _buffs)
            {
                for (int i = 0; i < buffPair.Value; i++)
                {
                    dummy = buffPair.Key.ProcessBuff(dummy);
                }
            }
            actualAttackDamage = Mathf.RoundToInt(dummy);
        }

        public void PrintBuffDictionary(){
            foreach (KeyValuePair<CombatBuff, int> buffPair in _buffs)
            {
                Debug.Log($"{name} has {buffPair.Value} {buffPair.Key._name} buffs");
            }
        }

        // Add Buff
        public void AddBuff(CombatBuff buff)
        {
            foreach (KeyValuePair<CombatBuff, int> buffPair in _buffs)
            {
                if (buffPair.Key._name == buff._name)
                {
                    _buffs[buffPair.Key] += 1;
                    combatEnemyUI.UpdateEnemyUI();  
                    return;
                }
            }
            _buffs.Add(buff, 1);
            combatEnemyUI.UpdateEnemyUI();  
        }

        // reduce the layer of all buffs by 1
        public void ReduceBuffLayer()
        {
            List<CombatBuff> buffsToRemove = new List<CombatBuff>();
            List<CombatBuff> buffsToReduce = new List<CombatBuff>();

            // Collect buffs to reduce or remove
            foreach (KeyValuePair<CombatBuff, int> buffPair in _buffs)
            {
                if (buffPair.Value > 1)
                {
                    buffsToReduce.Add(buffPair.Key);
                }
                else
                {
                    buffsToRemove.Add(buffPair.Key);
                }
            }

            // Reduce the layer of buffs
            foreach (CombatBuff buff in buffsToReduce)
            {
                _buffs[buff] -= 1;
            }

            // Remove buffs with only one layer left
            foreach (CombatBuff buff in buffsToRemove)
            {
                _buffs.Remove(buff);
            }
        }

#endregion
#region Attack and Heal and ActionLeft

        // Attack Player
        public void AttackPlayer()
        {
            Debug.Log($"{name} attacks player for {actualAttackDamage} damage!!!");
            _combatManager.PlayerTakeHit(actualAttackDamage);
        }

        // Change HP (which includes healing)
        public void ChangeHP(float amount)
        {
            _currentHP += Mathf.RoundToInt(amount);
            if (_currentHP > _maxHP)
            {
                _currentHP = _maxHP;
            }
            else if (_currentHP <= 0)
            {
                _currentHP = 0;
                // Handle enemy death
                Debug.Log($"{name} has been defeated.");
                _combatManager.RemoveEnemy(this);
            }
            
            // 更新UI显示（血条和HP文本）
            if (combatEnemyUI != null)
            {
                combatEnemyUI.UpdateEnemyUI();
            }
        }

#endregion
#region Getters       
        public int GetCurrentHP()
        {
            return _currentHP;
        }
#endregion 
    }

}
