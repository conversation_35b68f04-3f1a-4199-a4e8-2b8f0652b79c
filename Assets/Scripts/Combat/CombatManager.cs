using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;
#if UNITY_EDITOR
using Sirenix.OdinInspector;
#endif

namespace EOP.combat
{
    public class CombatManager : MonoBehaviour
    {
        [HideInInspector]
        private CombatPlayer _combatPlayer;

        [ShowInInspector, ReadOnly]
        [BoxGroup("Combat State")]
        [LabelText("Combat Enemies")]
        public List<CombatEnemyInterface> _combatEnemies;

        [ShowInInspector]
        [BoxGroup("Combat State")]
        [LabelText("Current Target")]
        public CombatEnemyInterface target;

        [FoldoutGroup("Legacy Layout Anchors")]
        [InfoBox("These anchors are kept for future top display functionality", InfoMessageType.Info)]
        [HideInInspector]
        public Transform Layout1Anchor1;
        [HideInInspector]
        public Transform Layout2Anchor1, Layout2Anchor2;
        [HideInInspector]
        public Transform Layout3Anchor1, Layout3Anchor2, Layout3Anchor3;
        [HideInInspector]
        public Transform Layout4Anchor1, Layout4Anchor2, Layout4Anchor3, Layout4Anchor4;
        [HideInInspector]
        public GameObject basicEnemyPrefab;

        [BoxGroup("Enemy System")]
        [LabelText("Enemy Generator")]
        [Required("Enemy Generator is required for enemy spawning")]
        public EOP.Enemy.EnemyGenerator enemyGenerator;

        [BoxGroup("Enemy Layout")]
        [LabelText("Use World Space Positioning")]
        [InfoBox("Toggle between UI Layout Group positioning and World Space positioning")]
        public bool useWorldSpacePositioning = false;

        [BoxGroup("Enemy Layout")]
        [LabelText("Enemies Layout Group")]
        [Required("Horizontal Layout Group is required for UI positioning")]
        [InfoBox("This Horizontal Layout Group will automatically position combat enemies when not using world space")]
        [ShowIf("@!useWorldSpacePositioning")]
        public HorizontalLayoutGroup enemiesLayoutGroup;

        [BoxGroup("Enemy Layout")]
        [LabelText("World Space Spacing")]
        [InfoBox("Distance between enemies when using world space positioning")]
        [ShowIf("useWorldSpacePositioning")]
        [Range(0.5f, 10f)]
        public float worldSpaceSpacing = 2f;

        [BoxGroup("Enemy Layout")]
        [LabelText("World Space Start Position")]
        [InfoBox("Starting position for the first enemy in world space")]
        [ShowIf("useWorldSpacePositioning")]
        public Vector3 worldSpaceStartPosition = new Vector3(-5f, 0f, 0f);

        [BoxGroup("Enemy Layout")]
        [LabelText("World Space Parent")]
        [InfoBox("Parent transform for world space enemies (optional)")]
        [ShowIf("useWorldSpacePositioning")]
        public Transform worldSpaceParent;

        [BoxGroup("Display System")]
        [LabelText("Display Layout Group")]
        [Required("Horizontal Layout Group is required for display prefab positioning")]
        [InfoBox("This Horizontal Layout Group will show enemy art/display prefabs")]
        public HorizontalLayoutGroup displayLayoutGroup;

        [FoldoutGroup("Legacy Settings")]
        [HideInInspector]
        public Transform EnemiesParent;

        [FoldoutGroup("Fall Time System")]
        [HideInInspector]
        public static float fallTime = 1f;
        [FoldoutGroup("Fall Time System")]
        [HideInInspector]
        public float fallTimee = 1f;
        [FoldoutGroup("Fall Time System")]
        [LabelText("Logarithmic Coefficient A")]
        [HideInInspector]
        public float a; // Coefficient for the logarithmic function
        [FoldoutGroup("Fall Time System")]
        [LabelText("Logarithmic Offset B")]
        [HideInInspector]
        public float b; // Constant offset for the logarithmic function

        [ShowInInspector, ReadOnly]
        [BoxGroup("Combat State")]
        [LabelText("Enemy Sequence")]
        public List<GameObject> enemySequence;

        // Removed manual positioning variables - now handled by HorizontalLayoutGroup
        // public float enemyScale = 0.7f;
        // public float enemySpacing = 6.4f;
        // public float enemyAnchorX = -1.3f;
        // public float enemyAnchorY = -6.73f;

        // Start is called before the first frame update
        void Start()
        {
            _combatPlayer = FindObjectOfType<CombatPlayer>();
            _combatEnemies = new List<CombatEnemyInterface>();
        }

        void Update()
        {
            fallTime = a * Mathf.Log(_combatPlayer._speed) + b;
            fallTimee = fallTime;
        }


        #region Combat Actions

        public void UpdateUIs()
        {
            UpdatePlayerUI();
            UpdateEnemiesUI();
        }

        public void UpdatePlayerUI()
        {
            _combatPlayer.GetComponent<CombatPlayerUI>().UpdatePlayerUI();
        }

        public void ChangePlayerHP(int amount)
        {
            _combatPlayer.ChangeHP(amount);
            UpdateUIs();
            Debug.Log($"Player HP changed by {amount}, current HP: {_combatPlayer.GetCurrentHP()}");
        }

        public void PlayerTakeHit(int amount)
        {
            _combatPlayer.TakeHit(amount);
            UpdateUIs();
        }

        public void ChangeTargetEnemyHP(int amount)
        {
            target.ChangeHP(amount);
            UpdateUIs();
        }

        // When an enemy is defeated, remove it from the list
        public void RemoveEnemy(CombatEnemyInterface enemy)
        {
            if (_combatEnemies.Contains(enemy))
            {
                // 获取敌人名称，用于清除对应的显示预制体
                string enemyName = enemy.name;

                // Remove from combat enemies list
                _combatEnemies.Remove(enemy);

                // Remove from enemy sequence
                if (enemySequence.Contains(enemy.gameObject))
                {
                    enemySequence.Remove(enemy.gameObject);
                }

                // Destroy the combat enemy
                Destroy(enemy.gameObject);

                // 清除对应的显示预制体
                if (enemyGenerator != null)
                {
                    enemyGenerator.ClearDisplayPrefabByEnemy(enemy);
                }

                // Update layout groups to reflect changes
                UpdateLayoutGroups();
                UpdateUIs();
            }
        }

        public void PlayerAttackTargetEnemy(int amount)
        {
            float damage = amount;
            damage = _combatPlayer.ProcessBuff(damage);
            if (target == null)
            {
                // Attack all enemies if target is null
                foreach (CombatEnemyInterface enemy in _combatEnemies)
                {
                    enemy.ChangeHP(-damage);
                }
            }
            else
            {
                target.ChangeHP(-damage);
            }

            UpdateUIs();
        }

        public int GetBuffLayer(CombatBuff buff)
        {
            return _combatPlayer.GetBuffLayer(buff);
        }

        public void ChangeRandomEnemyHP(int amount)
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            _combatEnemies[randomIndex].ChangeHP(amount);
            UpdateUIs();
        }

        public void ChangeSpecificEnemyHP(string enemyName, int amount)
        {
            CombatEnemyInterface enemy = findEnemy(enemyName);
            if (enemy != null)
            {
                enemy.ChangeHP(amount);
                UpdateUIs();
            }
        }

        public CombatEnemyInterface findEnemy(string enemyName)
        {
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                if (enemy.name == enemyName)
                {
                    return enemy;
                }
            }

            UpdateUIs();
            return null;
        }

        public void SetARandomEnemy()
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            target = _combatEnemies[randomIndex];
            UpdateUIs();
        }


        public void AddBuffToEnemy(CombatEnemyInterface target, CombatBuff buff)
        {
            target.AddBuff(buff);
            UpdateUIs();
        }

        public void AddBuffToPlayer(CombatBuff buff)
        {
            _combatPlayer.AddBuff(buff);
            UpdateUIs();
        }

        public void ChangeActionLeftForAllEnemies(int amount)
        {
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                _combatEnemies[i].ChangeActionLeft(amount);
            }

            UpdateUIs();
        }

        // add shield to player
        public void ChangePlayerShield(int amount)
        {
            _combatPlayer.AddBlock(amount);
            UpdateUIs();
        }

        // change the speed of the player
        public void ChangePlayerSpeed(int amount)
        {
            _combatPlayer.ChangeSpeed(amount);
            UpdateUIs();
        }

        #endregion

        //Generate Enemy

        #region Targeting Management

        public void TargetingFrontEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set target to the first enemy
                UpdateUIs();
            }
        }

        public void TargetingBackEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[_combatEnemies.Count - 1]; // Set target to the last enemy
                UpdateUIs();
            }
        }

        public void TargetingRandomEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                int randomIndex = Random.Range(0, _combatEnemies.Count);
                target = _combatEnemies[randomIndex]; // Set target to a random enemy
                UpdateUIs();
            }
        }

        public void TargetingAllEnemies()
        {
            if (_combatEnemies.Count > 0)
            {
                target = null; // Set target to null to indicate all enemies
                UpdateUIs();
            }
        }

        #endregion

        #region Enemy Generation and Management

        public void GenerateEnemy(List<string> enemyList)
        {
            // Clear previous enemies and display prefabs
            ClearAllEnemiesAndDisplays();

            for (int i = 0; i < enemyList.Count; i++)
            {
                string enemyName = enemyList[i];
                CombatEnemyInterface enemyInterface = null;
                GameObject displayPrefab = null;

                // 使用敌人生成器生成敌人
                if (enemyGenerator != null)
                {
                    // 生成战斗敌人，位置由HorizontalLayoutGroup管理
                    enemyInterface = enemyGenerator.GenerateEnemyByName(enemyName, Vector3.zero);

                    // 生成对应的显示预制体
                    displayPrefab = GenerateDisplayPrefab(enemyName);
                }
                else
                {
                    Debug.LogError("EnemyGenerator is not assigned!");
                }

                // 如果成功生成敌人，添加到列表中
                if (enemyInterface != null)
                {
                    _combatEnemies.Add(enemyInterface);

                    // 设置为战斗敌人布局组的子对象
                    if (enemiesLayoutGroup != null)
                    {
                        enemyInterface.transform.SetParent(enemiesLayoutGroup.transform, false);
                    }
                    else if (EnemiesParent != null)
                    {
                        enemyInterface.transform.parent = EnemiesParent;
                    }

                    // 布局组会自动处理缩放，但我们可以设置一个基础缩放
                    enemyInterface.transform.localScale = Vector3.one;
                    enemySequence.Add(enemyInterface.gameObject);

                    // 关联战斗敌人和显示预制体
                    if (displayPrefab != null && enemyGenerator != null)
                    {
                        enemyGenerator.AssignDisplayPrefabToEnemy(enemyInterface, displayPrefab);
                    }

                    Debug.Log($"Generated enemy: {enemyName}");
                }
                else
                {
                    Debug.LogWarning($"Failed to generate enemy: {enemyName}");
                }
            }

            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set initial target to the first enemy
            }

            // Update layout groups
            UpdateLayoutGroups();
        }

        // Update the enemy sequence - now managed by HorizontalLayoutGroup
        public void UpdateEnemySequence()
        {
            // HorizontalLayoutGroup automatically handles positioning
            // Just ensure enemies are properly parented and scaled
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                CombatEnemyInterface enemy = _combatEnemies[i];
                if (enemy != null)
                {
                    // Ensure proper parent
                    if (enemiesLayoutGroup != null && enemy.transform.parent != enemiesLayoutGroup.transform)
                    {
                        enemy.transform.SetParent(enemiesLayoutGroup.transform, false);
                    }

                    // Layout group handles scale, but we can set a base scale
                    enemy.transform.localScale = Vector3.one;
                }
            }
        }

        /// <summary>
        /// Generate a display prefab for the given enemy type
        /// </summary>
        private GameObject GenerateDisplayPrefab(string enemyName)
        {
            if (enemyGenerator == null || displayLayoutGroup == null)
            {
                Debug.LogWarning("EnemyGenerator or DisplayLayoutGroup not assigned!");
                return null;
            }

            // Get the appropriate display prefab from enemy generator
            GameObject displayPrefabTemplate = enemyGenerator.GetDisplayPrefab(enemyName);
            if (displayPrefabTemplate == null)
            {
                Debug.LogWarning($"No display prefab found for enemy: {enemyName}");
                return null;
            }

            // Instantiate the display prefab
            GameObject displayInstance = Instantiate(displayPrefabTemplate, Vector3.zero, Quaternion.identity);
            displayInstance.name = $"Display_{enemyName}_{_combatEnemies.Count}";

            // Set as child of display layout group
            displayInstance.transform.SetParent(displayLayoutGroup.transform, false);

            // Layout group will handle positioning
            displayInstance.transform.localScale = Vector3.one;

            Debug.Log($"Generated display prefab: {displayInstance.name}");
            return displayInstance;
        }

        /// <summary>
        /// Clear all enemies and their corresponding display prefabs
        /// </summary>
        private void ClearAllEnemiesAndDisplays()
        {
            // Clear combat enemies
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                if (enemy != null)
                {
                    Destroy(enemy.gameObject);
                }
            }
            _combatEnemies.Clear();
            enemySequence.Clear();

            // Clear display prefabs through enemy generator
            if (enemyGenerator != null)
            {
                enemyGenerator.ClearAllDisplayPrefabs();
            }

            // Also clear any remaining children in display layout group
            if (displayLayoutGroup != null)
            {
                for (int i = displayLayoutGroup.transform.childCount - 1; i >= 0; i--)
                {
                    Transform child = displayLayoutGroup.transform.GetChild(i);
                    if (child != null)
                    {
                        DestroyImmediate(child.gameObject);
                    }
                }
            }
        }

        /// <summary>
        /// Update both layout groups to refresh positioning
        /// </summary>
        private void UpdateLayoutGroups()
        {
            if (enemiesLayoutGroup != null)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(enemiesLayoutGroup.GetComponent<RectTransform>());
            }

            if (displayLayoutGroup != null)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(displayLayoutGroup.GetComponent<RectTransform>());
            }
        }

        public void UpdateEnemiesUI()
        {
            UpdateEnemySequence(); // Ensure enemies are in the correct order
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                enemy.GetComponent<CombatEnemyUI>().UpdateEnemyUI();
            }
        }

        /// <summary>
        /// 敌人向前移动（在序列中向左移动，索引减少）
        /// </summary>
        public void MoveEnemyForward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex > 0) // 不是第一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex - 1];
                _combatEnemies[currentIndex - 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex - 1];
                enemySequence[currentIndex - 1] = tempGameObject;

                // Update transform hierarchy to reflect new order
                if (enemiesLayoutGroup != null)
                {
                    enemy.transform.SetSiblingIndex(currentIndex - 1);
                }

                // 更新位置显示
                UpdateEnemySequence();
                UpdateLayoutGroups();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向前移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex - 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最前面，无法继续向前移动");
            }
        }

        /// <summary>
        /// 敌人向后移动（在序列中向右移动，索引增加）
        /// </summary>
        public void MoveEnemyBackward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex < _combatEnemies.Count - 1) // 不是最后一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex + 1];
                _combatEnemies[currentIndex + 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex + 1];
                enemySequence[currentIndex + 1] = tempGameObject;

                // Update transform hierarchy to reflect new order
                if (enemiesLayoutGroup != null)
                {
                    enemy.transform.SetSiblingIndex(currentIndex + 1);
                }

                // 更新位置显示
                UpdateEnemySequence();
                UpdateLayoutGroups();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向后移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex + 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最后面，无法继续向后移动");
            }
        }

        #region Testing Methods

        [FoldoutGroup("Testing")]
        [Button("Test Generate Mixed Enemies", ButtonSizes.Medium)]
        [InfoBox("Generate a mix of different enemy types for testing")]
        public void TestGenerateEnemies()
        {
            List<string> testEnemyList = new List<string>
            {
                "小偷大鹅",
                "面具疯子",
                "BasicEnemy"
            };

            GenerateEnemy(testEnemyList);
            Debug.Log("Test enemy generation completed");
        }

        [FoldoutGroup("Testing")]
        [Button("Test Generate Thief Goose", ButtonSizes.Medium)]
        public void TestGenerateThiefGoose()
        {
            List<string> testEnemyList = new List<string> { "小偷大鹅" };
            GenerateEnemy(testEnemyList);
        }

        [FoldoutGroup("Testing")]
        [Button("Test Generate Masked Maniac", ButtonSizes.Medium)]
        public void TestGenerateMaskedManiac()
        {
            List<string> testEnemyList = new List<string> { "面具疯子" };
            GenerateEnemy(testEnemyList);
        }

        [FoldoutGroup("Testing")]
        [Button("Clear All Enemies and Displays", ButtonSizes.Medium)]
        [InfoBox("Clear all enemies and their display prefabs")]
        public void TestClearAll()
        {
            ClearAllEnemiesAndDisplays();
            Debug.Log("Cleared all enemies and displays");
        }

        [FoldoutGroup("Testing")]
        [Button("Update Layout Groups", ButtonSizes.Medium)]
        [InfoBox("Force update both layout groups")]
        public void TestUpdateLayouts()
        {
            UpdateLayoutGroups();
            Debug.Log("Updated layout groups");
        }

        #endregion

        #endregion
    }
}