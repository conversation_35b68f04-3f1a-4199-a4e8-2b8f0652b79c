using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;
#if UNITY_EDITOR
using Sirenix.OdinInspector;
#endif

namespace EOP.combat
{
    public class CombatManager : MonoBehaviour
    {
        [HideInInspector]
        private CombatPlayer _combatPlayer;

        [ShowInInspector, ReadOnly]
        [BoxGroup("Combat State")]
        [LabelText("Combat Enemies")]
        public List<CombatEnemyInterface> _combatEnemies;

        [ShowInInspector]
        [BoxGroup("Combat State")]
        [LabelText("Current Target")]
        public CombatEnemyInterface target;

        [FoldoutGroup("Legacy Layout Anchors")]
        [InfoBox("These anchors are kept for future top display functionality", InfoMessageType.Info)]
        [HideInInspector]
        public Transform Layout1Anchor1;
        [HideInInspector]
        public Transform Layout2Anchor1, Layout2Anchor2;
        [HideInInspector]
        public Transform Layout3Anchor1, Layout3Anchor2, Layout3Anchor3;
        [HideInInspector]
        public Transform Layout4Anchor1, Layout4Anchor2, Layout4Anchor3, Layout4Anchor4;
        [HideInInspector]
        public GameObject basicEnemyPrefab;

        [BoxGroup("Enemy System")]
        [LabelText("Enemy Generator")]
        [Required("Enemy Generator is required for enemy spawning")]
        public EOP.Enemy.EnemyGenerator enemyGenerator;

        [BoxGroup("Enemy Layout")]
        [LabelText("Enemies Layout Group")]
        [Required("Horizontal Layout Group is required for enemy positioning")]
        [InfoBox("This Horizontal Layout Group will automatically position combat enemies")]
        public HorizontalLayoutGroup enemiesLayoutGroup;

        [BoxGroup("Display System")]
        [LabelText("Display Layout Group")]
        [Required("Horizontal Layout Group is required for display prefab positioning")]
        [InfoBox("This Horizontal Layout Group will show enemy art/display prefabs")]
        public HorizontalLayoutGroup displayLayoutGroup;

        [FoldoutGroup("Legacy Settings")]
        [HideInInspector]
        public Transform EnemiesParent;

        [FoldoutGroup("Fall Time System")]
        [HideInInspector]
        public static float fallTime = 1f;
        [FoldoutGroup("Fall Time System")]
        [HideInInspector]
        public float fallTimee = 1f;
        [FoldoutGroup("Fall Time System")]
        [LabelText("Logarithmic Coefficient A")]
        [HideInInspector]
        public float a; // Coefficient for the logarithmic function
        [FoldoutGroup("Fall Time System")]
        [LabelText("Logarithmic Offset B")]
        [HideInInspector]
        public float b; // Constant offset for the logarithmic function

        [ShowInInspector, ReadOnly]
        [BoxGroup("Combat State")]
        [LabelText("Enemy Sequence")]
        public List<GameObject> enemySequence;

        // Removed manual positioning variables - now handled by HorizontalLayoutGroup
        // public float enemyScale = 0.7f;
        // public float enemySpacing = 6.4f;
        // public float enemyAnchorX = -1.3f;
        // public float enemyAnchorY = -6.73f;

        // Start is called before the first frame update
        void Start()
        {
            _combatPlayer = FindObjectOfType<CombatPlayer>();
            _combatEnemies = new List<CombatEnemyInterface>();
        }

        void Update()
        {
            fallTime = a * Mathf.Log(_combatPlayer._speed) + b;
            fallTimee = fallTime;
        }


        #region Combat Actions

        public void UpdateUIs()
        {
            UpdatePlayerUI();
            UpdateEnemiesUI();
        }

        public void UpdatePlayerUI()
        {
            _combatPlayer.GetComponent<CombatPlayerUI>().UpdatePlayerUI();
        }

        public void ChangePlayerHP(int amount)
        {
            _combatPlayer.ChangeHP(amount);
            UpdateUIs();
            Debug.Log($"Player HP changed by {amount}, current HP: {_combatPlayer.GetCurrentHP()}");
        }

        public void PlayerTakeHit(int amount)
        {
            _combatPlayer.TakeHit(amount);
            UpdateUIs();
        }

        public void ChangeTargetEnemyHP(int amount)
        {
            target.ChangeHP(amount);
            UpdateUIs();
        }

        // When an enemy is defeated, remove it from the list
        public void RemoveEnemy(CombatEnemyInterface enemy)
        {
            if (_combatEnemies.Contains(enemy))
            {
                // 获取敌人名称，用于清除对应的显示预制体
                string enemyName = enemy.name;

                // Remove from combat enemies list
                _combatEnemies.Remove(enemy);

                // Remove from enemy sequence
                if (enemySequence.Contains(enemy.gameObject))
                {
                    enemySequence.Remove(enemy.gameObject);
                }

                // Destroy the combat enemy
                Destroy(enemy.gameObject);

                // 清除对应的显示预制体
                if (enemyGenerator != null)
                {
                    enemyGenerator.ClearDisplayPrefabByEnemy(enemy);
                }

                // Update layout groups to reflect changes
                UpdateLayoutGroups();
                UpdateUIs();
            }
        }

        public void PlayerAttackTargetEnemy(int amount)
        {
            float damage = amount;
            damage = _combatPlayer.ProcessBuff(damage);
            if (target == null)
            {
                // Attack all enemies if target is null
                foreach (CombatEnemyInterface enemy in _combatEnemies)
                {
                    enemy.ChangeHP(-damage);
                }
            }
            else
            {
                target.ChangeHP(-damage);
            }

            UpdateUIs();
        }

        public int GetBuffLayer(CombatBuff buff)
        {
            return _combatPlayer.GetBuffLayer(buff);
        }

        public void ChangeRandomEnemyHP(int amount)
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            _combatEnemies[randomIndex].ChangeHP(amount);
            UpdateUIs();
        }

        public void ChangeSpecificEnemyHP(string enemyName, int amount)
        {
            CombatEnemyInterface enemy = findEnemy(enemyName);
            if (enemy != null)
            {
                enemy.ChangeHP(amount);
                UpdateUIs();
            }
        }

        public CombatEnemyInterface findEnemy(string enemyName)
        {
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                if (enemy.name == enemyName)
                {
                    return enemy;
                }
            }

            UpdateUIs();
            return null;
        }

        public void SetARandomEnemy()
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            target = _combatEnemies[randomIndex];
            UpdateUIs();
        }


        public void AddBuffToEnemy(CombatEnemyInterface target, CombatBuff buff)
        {
            target.AddBuff(buff);
            UpdateUIs();
        }

        public void AddBuffToPlayer(CombatBuff buff)
        {
            _combatPlayer.AddBuff(buff);
            UpdateUIs();
        }

        public void ChangeActionLeftForAllEnemies(int amount)
        {
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                _combatEnemies[i].ChangeActionLeft(amount);
            }

            UpdateUIs();
        }

        // add shield to player
        public void ChangePlayerShield(int amount)
        {
            _combatPlayer.AddBlock(amount);
            UpdateUIs();
        }

        // change the speed of the player
        public void ChangePlayerSpeed(int amount)
        {
            _combatPlayer.ChangeSpeed(amount);
            UpdateUIs();
        }

        #endregion

        //Generate Enemy

        #region Targeting Management

        public void TargetingFrontEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set target to the first enemy
                UpdateUIs();
            }
        }

        public void TargetingBackEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[_combatEnemies.Count - 1]; // Set target to the last enemy
                UpdateUIs();
            }
        }

        public void TargetingRandomEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                int randomIndex = Random.Range(0, _combatEnemies.Count);
                target = _combatEnemies[randomIndex]; // Set target to a random enemy
                UpdateUIs();
            }
        }

        public void TargetingAllEnemies()
        {
            if (_combatEnemies.Count > 0)
            {
                target = null; // Set target to null to indicate all enemies
                UpdateUIs();
            }
        }

        #endregion

        #region Enemy Generation and Management

        public void GenerateEnemy(List<string> enemyList)
        {
            // Clear previous enemies
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                Destroy(enemy.gameObject);
            }

            _combatEnemies.Clear();
            enemySequence.Clear();

            for (int i = 0; i < enemyList.Count; i++)
            {
                string enemyName = enemyList[i];
                CombatEnemyInterface enemyInterface = null;

                // 使用敌人生成器生成敌人
                if (enemyGenerator != null)
                {
                    // 生成在原点，位置由HorizontalLayoutGroup管理
                    enemyInterface = enemyGenerator.GenerateEnemyByName(enemyName, Vector3.zero);
                }
                else
                {
                    Debug.LogError("EnemyGenerator is not assigned!");
                }

                // 如果成功生成敌人，添加到列表中
                if (enemyInterface != null)
                {
                    _combatEnemies.Add(enemyInterface);
                    
                    // 设置为HorizontalLayoutGroup的子对象
                    if (enemiesLayoutGroup != null)
                    {
                        enemyInterface.transform.SetParent(enemiesLayoutGroup.transform, false);
                    }
                    else
                    {
                        enemyInterface.transform.parent = EnemiesParent;
                    }
                    
                    enemyInterface.transform.localScale = Vector3.one * enemyScale;
                    enemySequence.Add(enemyInterface.gameObject);

                    Debug.Log($"Generated enemy: {enemyName}");
                }
                else
                {
                    Debug.LogWarning($"Failed to generate enemy: {enemyName}");
                }
            }

            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set initial target to the first enemy
            }
        }

        // Update the enemy sequence - now managed by HorizontalLayoutGroup
        public void UpdateEnemySequence()
        {
            // HorizontalLayoutGroup automatically handles positioning
            // Just ensure scale is correct
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                CombatEnemyInterface enemy = _combatEnemies[i];
                if (enemy != null)
                {
                    enemy.transform.localScale = Vector3.one * enemyScale;
                }
            }
        }

        public void UpdateEnemiesUI()
        {
            UpdateEnemySequence(); // Ensure enemies are in the correct order
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                enemy.GetComponent<CombatEnemyUI>().UpdateEnemyUI();
            }
        }

        /// <summary>
        /// 敌人向前移动（在序列中向左移动，索引减少）
        /// </summary>
        public void MoveEnemyForward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex > 0) // 不是第一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex - 1];
                _combatEnemies[currentIndex - 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex - 1];
                enemySequence[currentIndex - 1] = tempGameObject;

                // 更新位置显示
                UpdateEnemySequence();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向前移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex - 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最前面，无法继续向前移动");
            }
        }

        /// <summary>
        /// 敌人向后移动（在序列中向右移动，索引增加）
        /// </summary>
        public void MoveEnemyBackward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex < _combatEnemies.Count - 1) // 不是最后一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex + 1];
                _combatEnemies[currentIndex + 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex + 1];
                enemySequence[currentIndex + 1] = tempGameObject;

                // 更新位置显示
                UpdateEnemySequence();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向后移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex + 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最后面，无法继续向后移动");
            }
        }

        /// <summary>
        /// 测试生成敌人的方法
        /// </summary>
        [ContextMenu("Test Generate Enemies")]
        public void TestGenerateEnemies()
        {
            List<string> testEnemyList = new List<string>
            {
                "小偷大鹅",
                "面具疯子",
                "BasicEnemy"
            };

            GenerateEnemy(testEnemyList);
            Debug.Log("Test enemy generation completed");
        }

        /// <summary>
        /// 测试生成小偷大鹅
        /// </summary>
        [ContextMenu("Test Generate Thief Goose")]
        public void TestGenerateThiefGoose()
        {
            List<string> testEnemyList = new List<string> { "小偷大鹅" };
            GenerateEnemy(testEnemyList);
        }

        /// <summary>
        /// 测试生成面具疯子
        /// </summary>
        [ContextMenu("Test Generate Masked Maniac")]
        public void TestGenerateMaskedManiac()
        {
            List<string> testEnemyList = new List<string> { "面具疯子" };
            GenerateEnemy(testEnemyList);
        }

        #endregion
    }
}