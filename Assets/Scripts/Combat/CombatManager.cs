using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

namespace EOP.combat
{
    public class CombatManager : MonoBehaviour
    {
        private CombatPlayer _combatPlayer;
        public List<CombatEnemyInterface> _combatEnemies;

        public CombatEnemyInterface target;

        //Layout Anchors 这个还有用，因为最后是会在顶上显示的，但是现在先不用了
        public Transform Layout1Anchor1;
        public Transform Layout2Anchor1, Layout2Anchor2;
        public Transform Layout3Anchor1, Layout3Anchor2, Layout3Anchor3;
        public Transform Layout4Anchor1, Layout4Anchor2, Layout4Anchor3, Layout4Anchor4;
        public GameObject basicEnemyPrefab;

        [Header("敌人系统")] public EOP.Enemy.EnemyGenerator enemyGenerator; // 敌人生成器

        public Transform EnemiesParent;
        public static float fallTime = 1f;
        public float fallTimee = 1f;
        public float a; // Coefficient for the logarithmic function
        public float b; // Constant offset for the logarithmic function
        public List<GameObject> enemySequence;
        public float enemyScale = 0.7f;
        public float enemySpacing = 6.4f; // Spacing between enemies
        public float enemyAnchorX = -1.3f;
        public float enemyAnchorY = -6.73f;

        // Start is called before the first frame update
        void Start()
        {
            _combatPlayer = FindObjectOfType<CombatPlayer>();
            _combatEnemies = new List<CombatEnemyInterface>();
        }

        void Update()
        {
            fallTime = a * Mathf.Log(_combatPlayer._speed) + b;
            fallTimee = fallTime;
        }


        #region Combat Actions

        public void UpdateUIs()
        {
            UpdatePlayerUI();
            UpdateEnemiesUI();
        }

        public void UpdatePlayerUI()
        {
            _combatPlayer.GetComponent<CombatPlayerUI>().UpdatePlayerUI();
        }

        public void ChangePlayerHP(int amount)
        {
            _combatPlayer.ChangeHP(amount);
            UpdateUIs();
            Debug.Log($"Player HP changed by {amount}, current HP: {_combatPlayer.GetCurrentHP()}");
        }

        public void PlayerTakeHit(int amount)
        {
            _combatPlayer.TakeHit(amount);
            UpdateUIs();
        }

        public void ChangeTargetEnemyHP(int amount)
        {
            target.ChangeHP(amount);
            UpdateUIs();
        }

        // When an enemy is defeated, remove it from the list
        public void RemoveEnemy(CombatEnemyInterface enemy)
        {
            if (_combatEnemies.Contains(enemy))
            {
                // 获取敌人名称，用于清除对应的显示预制体
                string enemyName = enemy.name;

                _combatEnemies.Remove(enemy);
                Destroy(enemy.gameObject);
                UpdateUIs();

                // 清除对应的显示预制体
                if (enemyGenerator != null)
                {
                    enemyGenerator.ClearDisplayPrefabByEnemy(enemy);
                }
            }
        }

        public void PlayerAttackTargetEnemy(int amount)
        {
            float damage = amount;
            damage = _combatPlayer.ProcessBuff(damage);
            if (target == null)
            {
                // Attack all enemies if target is null
                foreach (CombatEnemyInterface enemy in _combatEnemies)
                {
                    enemy.ChangeHP(-damage);
                }
            }
            else
            {
                target.ChangeHP(-damage);
            }

            UpdateUIs();
        }

        public int GetBuffLayer(CombatBuff buff)
        {
            return _combatPlayer.GetBuffLayer(buff);
        }

        public void ChangeRandomEnemyHP(int amount)
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            _combatEnemies[randomIndex].ChangeHP(amount);
            UpdateUIs();
        }

        public void ChangeSpecificEnemyHP(string enemyName, int amount)
        {
            CombatEnemyInterface enemy = findEnemy(enemyName);
            if (enemy != null)
            {
                enemy.ChangeHP(amount);
                UpdateUIs();
            }
        }

        public CombatEnemyInterface findEnemy(string enemyName)
        {
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                if (enemy.name == enemyName)
                {
                    return enemy;
                }
            }

            UpdateUIs();
            return null;
        }

        public void SetARandomEnemy()
        {
            int randomIndex = Random.Range(0, _combatEnemies.Count);
            target = _combatEnemies[randomIndex];
            UpdateUIs();
        }


        public void AddBuffToEnemy(CombatEnemyInterface target, CombatBuff buff)
        {
            target.AddBuff(buff);
            UpdateUIs();
        }

        public void AddBuffToPlayer(CombatBuff buff)
        {
            _combatPlayer.AddBuff(buff);
            UpdateUIs();
        }

        public void ChangeActionLeftForAllEnemies(int amount)
        {
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                _combatEnemies[i].ChangeActionLeft(amount);
            }

            UpdateUIs();
        }

        // add shield to player
        public void ChangePlayerShield(int amount)
        {
            _combatPlayer.AddBlock(amount);
            UpdateUIs();
        }

        // change the speed of the player
        public void ChangePlayerSpeed(int amount)
        {
            _combatPlayer.ChangeSpeed(amount);
            UpdateUIs();
        }

        #endregion

        //Generate Enemy

        #region Targeting Management

        public void TargetingFrontEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set target to the first enemy
                UpdateUIs();
            }
        }

        public void TargetingBackEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[_combatEnemies.Count - 1]; // Set target to the last enemy
                UpdateUIs();
            }
        }

        public void TargetingRandomEnemy()
        {
            if (_combatEnemies.Count > 0)
            {
                int randomIndex = Random.Range(0, _combatEnemies.Count);
                target = _combatEnemies[randomIndex]; // Set target to a random enemy
                UpdateUIs();
            }
        }

        public void TargetingAllEnemies()
        {
            if (_combatEnemies.Count > 0)
            {
                target = null; // Set target to null to indicate all enemies
                UpdateUIs();
            }
        }

        #endregion

        #region Enemy Generation and Management

        public void GenerateEnemy(List<string> enemyList)
        {
            // Clear previous enemies
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                Destroy(enemy.gameObject);
            }

            _combatEnemies.Clear();
            enemySequence.Clear();

            for (int i = 0; i < enemyList.Count; i++)
            {
                string enemyName = enemyList[i];
                // Calculate horizontal position for each enemy in the group
                Vector3 spawnPosition = new Vector3(enemyAnchorX + i * enemySpacing, enemyAnchorY, 0);
                CombatEnemyInterface enemyInterface = null;

                // 使用敌人生成器生成敌人
                if (enemyGenerator != null)
                {
                    enemyInterface = enemyGenerator.GenerateEnemyByName(enemyName, spawnPosition);
                }
                else
                {
                    Debug.LogError("EnemyGenerator is not assigned!");
                }

                // 如果成功生成敌人，添加到列表中
                if (enemyInterface != null)
                {
                    _combatEnemies.Add(enemyInterface);
                    enemyInterface.transform.parent = EnemiesParent;
                    enemyInterface.transform.localScale = Vector3.one * enemyScale;
                    enemySequence.Add(enemyInterface.gameObject);

                    Debug.Log($"Generated enemy: {enemyName} at position {spawnPosition}");
                }
                else
                {
                    Debug.LogWarning($"Failed to generate enemy: {enemyName}");
                }
            }

            if (_combatEnemies.Count > 0)
            {
                target = _combatEnemies[0]; // Set initial target to the first enemy
            }
        }

        // Update the position of enemies so that they are always in the correct order
        public void UpdateEnemySequence()
        {
            for (int i = 0; i < _combatEnemies.Count; i++)
            {
                CombatEnemyInterface enemy = _combatEnemies[i];
                if (enemy != null)
                {
                    enemy.transform.position = new Vector3(enemyAnchorX + i * enemySpacing, enemyAnchorY, 0);
                    enemy.transform.localScale = Vector3.one * enemyScale;
                }
            }
        }

        public void UpdateEnemiesUI()
        {
            UpdateEnemySequence(); // Ensure enemies are in the correct order
            foreach (CombatEnemyInterface enemy in _combatEnemies)
            {
                enemy.GetComponent<CombatEnemyUI>().UpdateEnemyUI();
            }
        }

        /// <summary>
        /// 敌人向前移动（在序列中向左移动，索引减少）
        /// </summary>
        public void MoveEnemyForward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex > 0) // 不是第一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex - 1];
                _combatEnemies[currentIndex - 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex - 1];
                enemySequence[currentIndex - 1] = tempGameObject;

                // 更新位置显示
                UpdateEnemySequence();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向前移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex - 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最前面，无法继续向前移动");
            }
        }

        /// <summary>
        /// 敌人向后移动（在序列中向右移动，索引增加）
        /// </summary>
        public void MoveEnemyBackward(CombatEnemyInterface enemy)
        {
            int currentIndex = _combatEnemies.IndexOf(enemy);
            if (currentIndex < _combatEnemies.Count - 1) // 不是最后一个位置
            {
                // 交换位置
                _combatEnemies[currentIndex] = _combatEnemies[currentIndex + 1];
                _combatEnemies[currentIndex + 1] = enemy;

                // 同时更新enemySequence
                var tempGameObject = enemySequence[currentIndex];
                enemySequence[currentIndex] = enemySequence[currentIndex + 1];
                enemySequence[currentIndex + 1] = tempGameObject;

                // 更新位置显示
                UpdateEnemySequence();
                UpdateUIs();

                Debug.Log($"{enemy.name} 向后移动了一个位置 (从位置 {currentIndex} 移动到 {currentIndex + 1})");
            }
            else
            {
                Debug.Log($"{enemy.name} 已经在最后面，无法继续向后移动");
            }
        }

        /// <summary>
        /// 测试生成敌人的方法
        /// </summary>
        [ContextMenu("Test Generate Enemies")]
        public void TestGenerateEnemies()
        {
            List<string> testEnemyList = new List<string>
            {
                "小偷大鹅",
                "面具疯子",
                "BasicEnemy"
            };

            GenerateEnemy(testEnemyList);
            Debug.Log("Test enemy generation completed");
        }

        /// <summary>
        /// 测试生成小偷大鹅
        /// </summary>
        [ContextMenu("Test Generate Thief Goose")]
        public void TestGenerateThiefGoose()
        {
            List<string> testEnemyList = new List<string> { "小偷大鹅" };
            GenerateEnemy(testEnemyList);
        }

        /// <summary>
        /// 测试生成面具疯子
        /// </summary>
        [ContextMenu("Test Generate Masked Maniac")]
        public void TestGenerateMaskedManiac()
        {
            List<string> testEnemyList = new List<string> { "面具疯子" };
            GenerateEnemy(testEnemyList);
        }

        #endregion
    }
}