using System.Collections;
using System.Collections.Generic;
using EOP.combat;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using JetBrains.Annotations;
using System;

namespace EOP.combat{
public class CombatEnemyUI : MonoBehaviour
{
        private CombatEnemyInterface _enemyInterface;
        public TextMeshProUGUI _enemyHPText;
        
        [Header("血条系统")]
        public GameObject hpBarPrefab; // 血条预制体
        public Vector3 hpBarOffset = new Vector3(0, 5f, 0); // 血条相对于Canvas中心的偏移位置
        private GameObject currentHPBar; // 当前血条实例
        private Canvas targetCanvas; // 目标Canvas
        
        public GameObject nextActionIcon;
        public AssetFinder assetFinder;
        public GameObject buffAnchor; // it is in rect transform
        public GameObject _buff; // it is in rect transform
      
        private CombatManager _combatManager;
        public GameObject targetedFrame;

    // Start is called before the first frame update
    void Start()
    {
        // Find the combat enemy interface on this gameObject
        assetFinder = FindObjectOfType<AssetFinder>();
        _enemyInterface = GetComponent<CombatEnemyInterface>();
        _combatManager = FindObjectOfType<CombatManager>();
        
        // 查找Canvas
        FindTargetCanvas();
        
        // 初始化血条
        InitializeHPBar();
        
        UpdateEnemyUI();
    }
    
    /// <summary>
    /// 查找目标Canvas
    /// </summary>
    private void FindTargetCanvas()
    {
        // 直接查找敌人自己的Canvas
        targetCanvas = GetComponentInChildren<Canvas>();
        
        if (targetCanvas == null)
        {
            Debug.LogError("No Canvas found in enemy prefab! HP bar cannot be displayed.");
        }
        else
        {
            Debug.Log($"Found enemy Canvas: {targetCanvas.name}");
        }
    }
    
    /// <summary>
    /// 初始化血条
    /// </summary>
    private void InitializeHPBar()
    {
        if (hpBarPrefab != null && targetCanvas != null)
        {
            // 销毁现有的血条
            if (currentHPBar != null)
            {
                DestroyImmediate(currentHPBar);
            }
            
            // 实例化新的血条，作为Canvas的子物体
            currentHPBar = Instantiate(hpBarPrefab, targetCanvas.transform);
            
            // 设置血条位置
            currentHPBar.transform.localPosition = hpBarOffset;
            Debug.Log($"Set HP bar position to: {hpBarOffset}");
            
            // 调整血条大小
            RectTransform hpBarRect = currentHPBar.GetComponent<RectTransform>();
            if (hpBarRect != null)
            {
                // 血条大小可以通过Inspector中的RectTransform组件调整
                // 或者通过代码动态设置：hpBarRect.sizeDelta = new Vector2(宽度, 高度);
            }
            
            // 确保血条是激活的
            currentHPBar.SetActive(true);
            
            Debug.Log($"Initialized HP bar for {_enemyInterface?.name ?? "enemy"} in Canvas: {targetCanvas.name}");
            Debug.Log($"HP bar prefab name: {hpBarPrefab.name}");
            Debug.Log($"Current HP bar instance: {currentHPBar.name}");
            
            // 检查血条组件
            Slider hpBarSlider = currentHPBar.GetComponent<Slider>();
            if (hpBarSlider != null)
            {
                Debug.Log($"Found HP bar Slider component: {hpBarSlider.name}");
            }
            else
            {
                Debug.LogError("No Slider component found in HP bar prefab!");
            }
        }
        else
        {
            if (hpBarPrefab == null)
            {
                Debug.LogWarning("HP bar prefab not assigned!");
            }
            if (targetCanvas == null)
            {
                Debug.LogWarning("Target Canvas not found!");
            }
        }
    }
    
    public void UpdateEnemyUI()
    {
        _enemyHPText.text = $"{_enemyInterface.GetCurrentHP()}/{_enemyInterface._maxHP}";
        
        // 更新血条
        UpdateHPBar();
        
        // 更新血条位置（跟随敌人）
        UpdateHPBarPosition();
        
        targetedFrame.SetActive(_enemyInterface.targeted);
        
        // Clear existing buffs
        foreach (Transform child in buffAnchor.transform)
        {
            Destroy(child.gameObject);
        }

        // Instantiate buffs
        int index = 0;
        foreach (KeyValuePair<CombatBuff, int> buff in _enemyInterface._buffs)
        {
            GameObject buffInstance = Instantiate(_buff, buffAnchor.transform);
            buffInstance.transform.localPosition = new Vector3(index, 0, 0);

            IconObjectUI buffUI = buffInstance.GetComponent<IconObjectUI>();
            if (buffUI != null)
            {
                buffUI._buffIcon.sprite = assetFinder.FindImageForBuff(buff.Key);
                buffUI._subscriptText.text = buff.Value.ToString();
                buffUI._tooltipString = assetFinder.FindTooltipStringForBuff(buff.Key);
            }
            index++;
        }

        // Update next action icon
        IconObjectUI nextActionUI = nextActionIcon.GetComponent<IconObjectUI>();
        string nextAction = _enemyInterface._nextAction;
        
        // 尝试使用技能系统获取当前技能信息
        EOP.Enemy.EnemySkillManager skillManager = _enemyInterface.GetComponent<EOP.Enemy.EnemySkillManager>();
        if (skillManager != null)
        {
            // 使用新技能系统
            var currentSkillField = skillManager.GetType().GetField("currentSkill");
            
            if (currentSkillField != null)
            {
                var currentSkill = currentSkillField.GetValue(skillManager);
                
                if (currentSkill != null)
                {
                    // 通过反射获取技能数据
                    var skillNameField = currentSkill.GetType().GetField("skillName");
                    var effectTypeField = currentSkill.GetType().GetField("effectType");
                    var damageAmountField = currentSkill.GetType().GetField("damageAmount");
                    var healAmountField = currentSkill.GetType().GetField("healAmount");
                    
                    string skillName = skillNameField?.GetValue(currentSkill)?.ToString() ?? "Unknown";
                    var effectTypeValue = effectTypeField?.GetValue(currentSkill);
                    int damageAmount = (int)(damageAmountField?.GetValue(currentSkill) ?? 0);
                    int healAmount = (int)(healAmountField?.GetValue(currentSkill) ?? 0);
                    
                    // 设置图标
                    if (effectTypeValue != null && System.Enum.IsDefined(typeof(EOP.Enemy.EnemySkillEffectType), effectTypeValue))
                    {
                        EOP.Enemy.EnemySkillEffectType effectType = (EOP.Enemy.EnemySkillEffectType)effectTypeValue;
                        nextActionUI._buffIcon.sprite = assetFinder.FindImageForEnemySkillEffect(effectType);
                        nextActionUI._tooltipString = assetFinder.FindTooltipForEnemySkillEffect(effectType, skillName);
                        
                        // 设置技能数值显示
                        if (effectType == EOP.Enemy.EnemySkillEffectType.Attack && damageAmount > 0)
                        {
                            nextActionUI._superscriptText.text = damageAmount.ToString();
                        }
                        else if ((effectType == EOP.Enemy.EnemySkillEffectType.HealSelf || effectType == EOP.Enemy.EnemySkillEffectType.HealOther) && healAmount > 0)
                        {
                            nextActionUI._superscriptText.text = healAmount.ToString();
                        }
                        else
                        {
                            nextActionUI._superscriptText.text = "";
                        }
                        
                        // 设置行动计数
                        nextActionUI._subscriptText.text = _enemyInterface._actionsLeft.ToString();
                    }
                    else
                    {
                        // 回退到旧方法
                        SetNextActionUIFallback(nextActionUI, nextAction);
                    }
                }
                else
                {
                    // 回退到旧方法
                    SetNextActionUIFallback(nextActionUI, nextAction);
                }
            }
            else
            {
                // 回退到旧方法
                SetNextActionUIFallback(nextActionUI, nextAction);
            }
        }
        else
        {
            // 使用旧技能系统
            SetNextActionUIFallback(nextActionUI, nextAction);
        }
        
    }
    
    /// <summary>
    /// 更新血条显示
    /// </summary>
    private void UpdateHPBar()
    {
        if (currentHPBar != null)
        {
            // 查找血条中的Slider组件
            Slider hpBarSlider = currentHPBar.GetComponent<Slider>();
            
            if (hpBarSlider != null)
            {
                float fillAmount = (float)_enemyInterface.GetCurrentHP() / (float)_enemyInterface._maxHP;
                hpBarSlider.value = fillAmount;
                
                // 添加调试信息（只在第一次更新时显示）
                Debug.Log($"Updated HP bar value: {fillAmount} (HP: {_enemyInterface.GetCurrentHP()}/{_enemyInterface._maxHP})");
            }
            else
            {
                Debug.LogWarning("HP bar prefab does not contain a Slider component!");
            }
        }
        else
        {
            Debug.LogWarning("Current HP bar is null! Trying to reinitialize...");
            InitializeHPBar();
        }
    }
    
    /// <summary>
    /// 更新血条位置（跟随敌人）
    /// </summary>
    private void UpdateHPBarPosition()
    {
        if (currentHPBar != null && targetCanvas != null)
        {
            // World Space Canvas，直接设置localPosition
            currentHPBar.transform.localPosition = hpBarOffset;
        }
    }
    
    /// <summary>
    /// 重新初始化血条（用于运行时更换血条样式）
    /// </summary>
    public void ReinitializeHPBar()
    {
        InitializeHPBar();
        UpdateHPBar();
    }
    
    /// <summary>
    /// 强制重新初始化血条（调试用）
    /// </summary>
    [ContextMenu("Force Reinitialize HP Bar")]
    public void ForceReinitializeHPBar()
    {
        Debug.Log("Force reinitializing HP bar...");
        FindTargetCanvas();
        InitializeHPBar();
        UpdateHPBar();
    }
    
    /// <summary>
    /// 使用旧系统设置下一个行动的UI（回退方法）
    /// </summary>
    private void SetNextActionUIFallback(IconObjectUI nextActionUI, string nextAction)
    {
        nextActionUI._buffIcon.sprite = assetFinder.FindImageForAction(nextAction);
        nextActionUI._subscriptText.text = _enemyInterface._actionsLeft.ToString();
        if (nextAction == "Attack")
        {
            nextActionUI._superscriptText.text = _enemyInterface.actualAttackDamage.ToString();
        }
        else if (nextAction == "Heal")
        {
            nextActionUI._superscriptText.text = _enemyInterface.basicHealAmount.ToString();
        }
        else
        {
            nextActionUI._superscriptText.text = "";
        }
        nextActionUI._tooltipString = $"下一个行动: {nextAction}";
    }
    
    // Helper functions for finding the correct image for the buff and action
}
}
