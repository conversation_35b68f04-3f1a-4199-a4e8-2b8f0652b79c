using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.combat;

namespace EOP.Skills.Effects
{
    public class SpecialSkillEffect : SkillEffect
    {
        [Header("特殊效果设置")]
        public Dictionary<string, int> skillCounters = new Dictionary<string, int>();

        public override void Execute(SkillData skillData, int clearCount, float dropTime)
        {
            if (!CanExecute(skillData)) return;

            // 根据技能ID执行特殊效果
            ExecuteSpecificEffect(skillData, clearCount, dropTime);
            
            Debug.Log($"执行特殊技能: {skillData.skillName}");
        }

        private void ExecuteSpecificEffect(SkillData skillData, int clearCount, float dropTime)
        {
            switch (skillData.skillId)
            {
                case "BR12":
                    HandleBR12Effect(skillData, clearCount);
                    break;
                    
                case "BR13":
                    HandleBR13Effect(skillData, clearCount);
                    break;
                    
                case "BR17":
                    HandleBR17Effect(skillData, clearCount);
                    break;
                    
                default:
                    Debug.LogWarning($"未实现的特殊技能效果: {skillData.skillId}");
                    break;
            }
        }

        /// <summary>
        /// 处理BR12技能效果 - 蓄力
        /// </summary>
        private void HandleBR12Effect(SkillData skillData, int clearCount)
        {
            string counterKey = "BR12Count";
            
            if (!skillCounters.ContainsKey(counterKey))
            {
                skillCounters[counterKey] = 0;
            }
            
            skillCounters[counterKey] += skillData.baseValue;
            Debug.Log($"BR12计数器增加: {skillData.baseValue}, 当前值: {skillCounters[counterKey]}");
        }

        /// <summary>
        /// 处理BR13技能效果 - 高速直觉
        /// </summary>
        private void HandleBR13Effect(SkillData skillData, int clearCount)
        {
            if (!CheckSpeedCondition(skillData)) return;
            
            // 获得行动次数
            // 这里需要实现行动次数的逻辑，可能需要在CombatPlayer中添加
            Debug.Log("获得额外行动次数");
            
            // 设置直觉状态
            SetInstinctState(true);
        }

        /// <summary>
        /// 处理BR17技能效果 - 预留技能
        /// </summary>
        private void HandleBR17Effect(SkillData skillData, int clearCount)
        {
            // 预留技能位，暂时不做任何操作
            Debug.Log("预留技能效果");
        }

        /// <summary>
        /// 设置直觉状态
        /// </summary>
        private void SetInstinctState(bool state)
        {
            // 这里需要实现直觉状态的设置
            // 可能需要在CombatPlayer中添加相应的状态管理
            Debug.Log($"设置直觉状态: {state}");
        }

        /// <summary>
        /// 获取技能计数器值
        /// </summary>
        public int GetSkillCounter(string counterKey)
        {
            return skillCounters.ContainsKey(counterKey) ? skillCounters[counterKey] : 0;
        }

        /// <summary>
        /// 设置技能计数器值
        /// </summary>
        public void SetSkillCounter(string counterKey, int value)
        {
            skillCounters[counterKey] = value;
        }

        /// <summary>
        /// 消耗技能计数器
        /// </summary>
        public bool ConsumeSkillCounter(string counterKey, int amount)
        {
            if (!skillCounters.ContainsKey(counterKey) || skillCounters[counterKey] < amount)
            {
                return false;
            }
            
            skillCounters[counterKey] -= amount;
            return true;
        }

        public override bool CanExecute(SkillData skillData)
        {
            if (!base.CanExecute(skillData)) return false;
            
            // 根据技能ID检查特殊条件
            switch (skillData.skillId)
            {
                case "BR13":
                    return CheckSpeedCondition(skillData);
                    
                default:
                    return true;
            }
        }
    }
}
