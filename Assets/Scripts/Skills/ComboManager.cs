using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Skills
{
    /// <summary>
    /// 连击管理器 - 处理技能连击逻辑和暴击计算
    /// </summary>
    public class ComboManager : MonoBehaviour
    {
        [Header("连击设置")] [SerializeField] private bool enableComboSystem = true;
        [SerializeField] private float comboResetTime = 5f; // 连击重置时间

        [Header("调试信息")] [SerializeField] private bool showDebugInfo = false;

        // 连击状态
        [ShowInInspector] [ReadOnly] private SkillData lastUsedSkill;
        private float lastSkillTime;

        [ShowInInspector] [ReadOnly] [SerializeField]
        private int currentComboChain = 0;

        // 事件
        public System.Action<SkillData, int> OnComboTriggered;
        public System.Action<SkillData, int, bool> OnDamageDealt; // skill, damage, isCritical

        private void Update()
        {
            // 检查连击是否超时
            if (lastUsedSkill != null && Time.time - lastSkillTime > comboResetTime)
            {
                ResetCombo();
            }
        }

        /// <summary>
        /// 使用技能并计算连击
        /// </summary>
        public int UseSkill(SkillData skill)
        {
            if (!enableComboSystem || skill == null)
            {
                return skill?.comboCount ?? 1;
            }

            int totalComboCount;

            // 检查是否触发连击累积
            if (lastUsedSkill != null && skill.HasSharedTagsWith(lastUsedSkill))
            {
                // 累积连击：当前连击链 + 新技能的连击数
                currentComboChain += skill.comboCount;
                totalComboCount = currentComboChain;

                OnComboTriggered?.Invoke(skill, totalComboCount);

                if (showDebugInfo)
                {
                    Debug.Log($"连击累积! {lastUsedSkill.skillName} → {skill.skillName}, 累积连击数: {totalComboCount}");
                }
            }
            else
            {
                // 重新开始连击
                currentComboChain = skill.comboCount;
                totalComboCount = currentComboChain;

                if (showDebugInfo)
                {
                    Debug.Log($"连击重新开始! {skill.skillName}, 连击数: {totalComboCount}");
                }
            }

            // 更新状态
            lastUsedSkill = skill;
            lastSkillTime = Time.time;

            return totalComboCount;
        }

        /// <summary>
        /// 执行连击攻击
        /// </summary>
        public void ExecuteComboAttack(SkillData skill, System.Action<int, bool> onHitCallback)
        {
            if (skill == null || onHitCallback == null)
                return;

            int totalComboCount = UseSkill(skill);
            StartCoroutine(ExecuteComboCoroutine(skill, totalComboCount, onHitCallback));
        }

        /// <summary>
        /// 连击攻击协程
        /// </summary>
        private IEnumerator ExecuteComboCoroutine(SkillData skill, int comboCount,
            System.Action<int, bool> onHitCallback)
        {
            for (int i = 0; i < comboCount; i++)
            {
                // 计算伤害
                int damage = skill.baseValue;
                bool isCritical = skill.RollCriticalHit();

                if (isCritical)
                {
                    damage = skill.CalculateCriticalDamage(damage);
                }

                // 执行伤害
                onHitCallback?.Invoke(damage, isCritical);
                OnDamageDealt?.Invoke(skill, damage, isCritical);

                if (showDebugInfo)
                {
                    string critText = isCritical ? " (暴击!)" : "";
                }

                // 等待间隔时间（除了最后一击）
                if (i < comboCount - 1)
                {
                    yield return new WaitForSeconds(skill.hitInterval);
                }
            }
        }

        /// <summary>
        /// 重置连击状态
        /// </summary>
        public void ResetCombo()
        {
            lastUsedSkill = null;
            currentComboChain = 0;

            if (showDebugInfo)
            {
                Debug.Log("连击重置");
            }
        }

        /// <summary>
        /// 获取当前连击信息
        /// </summary>
        public ComboInfo GetCurrentComboInfo()
        {
            return new ComboInfo
            {
                lastSkill = lastUsedSkill,
                comboChain = currentComboChain,
                timeRemaining = lastUsedSkill != null ? comboResetTime - (Time.time - lastSkillTime) : 0f
            };
        }

        /// <summary>
        /// 预览下一个技能的连击效果
        /// </summary>
        public ComboPreview PreviewSkillCombo(SkillData skill)
        {
            if (skill == null)
                return new ComboPreview();

            bool willTriggerCombo = lastUsedSkill != null && skill.HasSharedTagsWith(lastUsedSkill);
            int totalComboCount = willTriggerCombo ? currentComboChain + skill.comboCount : skill.comboCount;

            return new ComboPreview
            {
                totalComboCount = totalComboCount,
                willTriggerCombo = willTriggerCombo,
                currentChain = currentComboChain,
                sharedTags = willTriggerCombo ? skill.GetSharedTagCount(lastUsedSkill) : 0
            };
        }
    }

    /// <summary>
    /// 连击信息结构
    /// </summary>
    [System.Serializable]
    public struct ComboInfo
    {
        public SkillData lastSkill;
        public int comboChain;
        public float timeRemaining;
    }

    /// <summary>
    /// 连击预览结构
    /// </summary>
    [System.Serializable]
    public struct ComboPreview
    {
        public int totalComboCount;
        public bool willTriggerCombo;
        public int currentChain;
        public int sharedTags;
    }
}