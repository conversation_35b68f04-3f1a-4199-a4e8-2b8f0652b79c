using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace EOP.Data
{
    [Obsolete("This class is deprecated, use TetrominoSO instead.")]
    [CreateAssetMenu(fileName = "New TetrisShapeSO", menuName = "Data/TetrisShapeSO")]
    public class TetrisShapeSO: ScriptableObject
    {
        [Tooltip("The shape starts at (0, 0)")]
	    public Vector2[] TetrisPiecesPos;
    }
}
