using UnityEditor;
using UnityEngine;
using EOP.data;

[CustomEditor(typeof(RevolverConfig))]
public class RevolverConfigEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        if (GUILayout.But<PERSON>("Initialize with Hardcoded Data"))
        {
            RevolverConfig revolverConfig = (RevolverConfig)target;
            revolverConfig.InitializeWithHardcodedData();
            EditorUtility.SetDirty(revolverConfig);
            Debug.Log("RevolverConfig initialized with hardcoded data!");
        }
    }
}
